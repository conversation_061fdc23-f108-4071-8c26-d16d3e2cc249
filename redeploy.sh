#!/bin/bash

# Usage:
#   ./redeploy.sh -u GITHUB_USER -p GITHUB_PAT -b BOT_ACCOUNT -- TOKEN1 TOKEN2 TOKEN3 ...
#
#   -u : GitHub username
#   -p : GitHub Personal Access Token
#   -b : Bot account value for BOT_ACCOUNT
#   -- : (required) separates options from tokens
#   TOKEN1 TOKEN2 ... : List of tokens to deploy

GITHUB_USER=""
GITHUB_PAT=""
BOT_ACCOUNT=""

# Parse options
while getopts u:p:b: option; do
    case "${option}" in
        u) GITHUB_USER=${OPTARG};;
        p) GITHUB_PAT=${OPTARG};;
        b) BOT_ACCOUNT=${OPTARG};;
    esac
done
shift $((OPTIND-1))

# Require -- to separate options from tokens
if [[ "$1" == "--" ]]; then
    shift
fi

# All remaining arguments are tokens
TOKENS=("$@")

echo "Stopping all running Docker containers..."
sudo docker stop $(sudo docker ps -q) || echo "No running containers to stop."

echo "Resetting the repository to the latest version..."
git reset --hard

echo "Cleaning up old files..."
sudo rm -f config/traders/*.pkl
sudo rm -f log_files/*.log

# Handle git pull with credentials if provided
if [ -n "$GITHUB_USER" ] && [ -n "$GITHUB_PAT" ]; then
    echo "Pulling the latest code with provided credentials..."
    ORIGINAL_URL=$(git config --get remote.origin.url)
    REPO_URL=$(echo "$ORIGINAL_URL" | sed -e "s|https://|https://${GITHUB_USER}:${GITHUB_PAT}@|")
    git remote set-url origin "$REPO_URL"
    git pull
    git remote set-url origin "$ORIGINAL_URL"
else
    echo "Pulling the latest code..."
    git pull
fi

# Update TOKENS in run.sh if any tokens were provided
if [ "${#TOKENS[@]}" -gt 0 ] && [ -n "${TOKENS[0]}" ]; then
    # Build the TOKENS array assignment
    tokens_string="TOKENS=("
    for token in "${TOKENS[@]}"; do
        tokens_string+="\"$token\" "
    done
    tokens_string="${tokens_string% }"  # Remove trailing space
    tokens_string+=")"
    # Replace the TOKENS line in run.sh
    sed -i.bak "s/^TOKENS=.*$/${tokens_string}/" bash_scripts/run.sh
    echo "Updated TOKENS in bash_scripts/run.sh: $tokens_string"
else
    echo "No tokens provided, using the default tokens in run.sh"
fi

# Update BOT_ACCOUNT in run.sh if provided
if [ -n "$BOT_ACCOUNT" ]; then
    sed -i.bak "s/^BOT_ACCOUNT=.*/BOT_ACCOUNT=\"$BOT_ACCOUNT\"/" bash_scripts/run.sh
    echo "Updated BOT_ACCOUNT in bash_scripts/run.sh: $BOT_ACCOUNT"
fi

echo "Running the main application script..."
sudo bash bash_scripts/run.sh

echo "Redeployment script finished."

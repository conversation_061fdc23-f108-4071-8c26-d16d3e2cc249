# Use an official Python runtime as a base image
FROM python:3.11-slim

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file into the container
COPY requirements.txt .

# Install Git and Python dependencies
RUN pip3 install --upgrade pip
RUN python3 -m pip install --upgrade setuptools
RUN apt-get update && apt-get install -y git && pip install -r requirements.txt

# Copy the rest of the application code into the container
COPY . .

# Specify the command to run your Python application
CMD ["python3", "main.py"]
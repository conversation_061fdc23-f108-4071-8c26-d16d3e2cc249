import time
from datetime import datetime

import pandas as pd
import requests
from binance import Client, BinanceAPIException, BinanceRequestException

from common.enums.decision import Decision
from common.enums.time_interval import TimeInterval
from logic.data_containers.trader_parameters import TraderParameters
from services.exchange_services.exchange_base import ExchangeBase
from services.exchange_services.utils.health_decorator import binance_health_check
from services.exchange_services.utils.retry_decorator import retry


class BinanceExchange(ExchangeBase):
    # constants
    MAX_RETRIES = 5
    SLEEP_TIME = 0.2

    max_candle_count = 350
    contract_type = "PERPETUAL"

    def __init__(self, secrets, logger=None):
        super().__init__()
        self.client = Client(secrets["api_key"], secrets["api_secret"])
        self.logger = logger
        self.is_isolated = False
        if self.logger:
            self.logger.info("Initializing Binance Exchange")
        self.quantity_precisions = self.get_binance_spot_quantity_precisions()
        if self.logger:
            self.logger.info(f"Loaded {len(self.quantity_precisions)} quantity precisions from Binance")

    def adjust_quantity(self, symbol, quantity):
        try:
            symbol_info = self.client.futures_exchange_info()
            symbol_data = next(s for s in symbol_info['symbols'] if s['symbol'] == symbol)
            precision = symbol_data['quantityPrecision']

            format_str = "{:0." + str(precision) + "f}"
            adjusted = format_str.format(quantity)

            if self.logger:
                self.logger.debug(f"Adjusted quantity for {symbol}: {quantity} → {adjusted} (precision: {precision})")
            return adjusted
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error adjusting quantity for {symbol}: {e}")
            raise e

    @retry(max_retries=MAX_RETRIES, delay=SLEEP_TIME)
    @binance_health_check()
    def get_and_check_candle(self, symbol: str, interval):
        candle_data = self.get_historical_data(symbol, interval)
        is_valid_candle = self.check_candle_data(candle_data, interval)

        while not is_valid_candle:
            self.logger.warning(f"Invalid candle data received for {symbol}, retrying...")
            time.sleep(0.2)
            candle_data = self.get_historical_data(symbol, interval)
            is_valid_candle = self.check_candle_data(candle_data, interval)

        self.logger.warning(f"Valid candle data received for {symbol} with {len(candle_data)} entries")
        return candle_data

    def check_candle_data(self, candle: pd.DataFrame, interval: TimeInterval) -> bool:
        if candle.empty:
            self.logger.info(f"Candle data missing, CONTINUE")
            return False
        current_time = datetime.utcnow()
        shaved_current_time = self.shave_time(current_time, interval)
        last_candle_timestamp = candle['time'].iloc[-1]
        last_candle_datetime = datetime.utcfromtimestamp(last_candle_timestamp / 1000)

        if shaved_current_time == last_candle_datetime:
            if self.logger:
                self.logger.debug(
                    f"Candle validation successful: current={shaved_current_time}, last={last_candle_datetime}")
            return True

        if self.logger:
            self.logger.debug(f"Candle validation failed: current={shaved_current_time}, last={last_candle_datetime}")
        return False

    @staticmethod
    def shave_time(current_time, resolution: TimeInterval):
        if resolution == TimeInterval.MINUTE:
            return current_time.replace(second=0, microsecond=0)
        elif resolution == TimeInterval.FIVE_MIN:
            minutes = (current_time.minute // 5) * 5
            return current_time.replace(minute=minutes, second=0, microsecond=0)
        elif resolution == TimeInterval.FIFTEEN_MIN:
            minutes = (current_time.minute // 15) * 15
            return current_time.replace(minute=minutes, second=0, microsecond=0)
        elif resolution == TimeInterval.THIRTY_MIN:
            minutes = (current_time.minute // 30) * 30
            return current_time.replace(minute=minutes, second=0, microsecond=0)
        elif resolution == TimeInterval.ONE_HOUR:
            return current_time.replace(minute=0, second=0, microsecond=0)
        elif resolution == TimeInterval.FOUR_HOUR:
            hours = (current_time.hour // 4) * 4
            return current_time.replace(hour=hours, minute=0, second=0, microsecond=0)
        else:
            return None

    @retry(max_retries=MAX_RETRIES, delay=SLEEP_TIME)
    @binance_health_check()
    def get_current_price(self, symbol):
        try:
            if self.logger:
                self.logger.debug(f"Fetching current spot price for {symbol}")
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            price = float(ticker['price'])
            if self.logger:
                self.logger.info(f"Current spot price for {symbol}: {price}")
            return price
        except (BinanceAPIException, BinanceRequestException) as e:
            if self.logger:
                self.logger.error(f"Error fetching spot price for {symbol}: {e}")
            else:
                print(f"Error fetching current price for {symbol}: {e}")
            raise e


    def isolate_trade(self, symbol):
        try:
            self.client.futures_change_margin_type(symbol=symbol, marginType='ISOLATED')  # or 'CROSS'
            self.is_isolated = True
            self.logger.debug(f"Leverage set to ISOLATED margin for {symbol}")
        except BinanceAPIException as e:
            if e.code == -4046:
                # Ignore the error and continue since margin type is already 'ISOLATED'
                self.logger.info(f"Margin type for {symbol} is already 'ISOLATED', skipping margin change.")
            else:
                # Log other BinanceAPIException errors
                self.logger.error(f"Error changing margin type for {symbol}: {e}")
                return None  # or handle accordingly

    @retry(max_retries=MAX_RETRIES, delay=SLEEP_TIME)
    @binance_health_check()
    def execute_order(self, symbol, side, amount, leverage, reduce_only=False):
        """
        Execute an order on the exchange with the specified parameters.
        
        Args:
            symbol: Trading pair symbol
            side: 'BUY' or 'SELL'
            amount: Amount to trade
            leverage: Leverage to use
            reduce_only: Whether this order should only reduce position
            
        Returns:
            Order result from the exchange
        """
        try:
            self.logger.info(f"Executing {side} order for {symbol} with amount {amount} at {leverage}x leverage")
            
            # Ensure the exchange is properly configured
            if not self.is_isolated:
                self.isolate_trade(symbol)
                
            # Set leverage
            self.client.futures_change_leverage(symbol=symbol, leverage=leverage)
            self.logger.debug(f"Leverage set to {leverage}x for {symbol}")
            
            # Adjust the quantity according to exchange requirements
            adjusted_amount = self.adjust_quantity(symbol, amount)
            
            # Create the order
            order_params = {
                'symbol': symbol,
                'side': side,
                'type': 'MARKET',
                'quantity': adjusted_amount
            }
            
            # Add reduceOnly parameter if needed
            if reduce_only:
                order_params['reduceOnly'] = True
                
            # Execute the order
            order = self.client.futures_create_order(**order_params)
            
            action_type = "Closing" if reduce_only else "Opening"
            position_type = "LONG" if side == 'BUY' and not reduce_only else "SHORT" if side == 'SELL' and not reduce_only else "position"
            
            self.logger.info(f"{action_type} {position_type} for {symbol}: Order ID {order['orderId']}")
            return order
            
        except (BinanceAPIException, BinanceRequestException) as e:
            self.logger.error(f"Error executing {side} order for {symbol}: {e}")
            raise e

    @retry(max_retries=MAX_RETRIES, delay=SLEEP_TIME)
    @binance_health_check()
    def get_current_futures_price(self, symbol) -> float:
        try:
            ticker = self.client.futures_symbol_ticker(symbol=symbol)
            price = float(ticker['price'])
            return price
        except (BinanceAPIException, BinanceRequestException) as e:
            self.logger.error(f"Error fetching futures price for {symbol}: {e}")
            raise e

    @retry(max_retries=MAX_RETRIES, delay=SLEEP_TIME)
    @binance_health_check()
    def get_historical_data(self, symbol, interval):
        try:
            # Convert TimeInterval enum to string if needed
            interval_str = interval.value if hasattr(interval, 'value') else interval

            candles = self.client.futures_continous_klines(
                pair=symbol,
                interval=interval_str,
                contractType=self.__class__.contract_type,
                limit=self.__class__.max_candle_count
            )
            data = [{
                "time": candle[0],
                "datetime": datetime.utcfromtimestamp(candle[0] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                # Human readable time
                "open": float(candle[1]),
                "high": float(candle[2]),
                "low": float(candle[3]),
                "close": float(candle[4]),
                "volume": float(candle[5])
            } for candle in candles]

            df = pd.DataFrame(data)
            self.logger.debug(
                f"Historical data processed for {symbol}, first timestamp: {data[0]['datetime']}, last: {data[-1]['datetime']}")
            return df

        except (BinanceAPIException, BinanceRequestException) as e:
            self.logger.error(f"BINANCE Error fetching historical data for {symbol}: {e}")
            raise e
        except Exception as e:
            self.logger.error(f"UNEXPECTEDError fetching historical data for {symbol}: {e}")
            raise e

    def get_binance_spot_quantity_precisions(self):
        try:

            url = "https://api1.binance.com/api/v3/exchangeInfo"
            response = requests.get(url)
            response_data = response.json()
            tmp = {}
            # Parse the response to get tick size for the given symbol
            for s in response_data['symbols']:
                filters = s.get("filters")
                quantity_precision = 0
                for filter_ in filters:
                    if filter_.get("filterType") == "LOT_SIZE":
                        step_size = filter_.get("stepSize")
                        if '.' in step_size:
                            point_index = step_size.index('.')
                            one_index = step_size.index('1')
                            quantity_precision = one_index - point_index
                        else:
                            quantity_precision = 0
                tmp[s['symbol']] = quantity_precision
            return tmp

        except Exception as e:
            self.logger.error(f"Error fetching quantity precisions: {e}")
            raise e

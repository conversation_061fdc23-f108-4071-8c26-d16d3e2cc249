import smtplib
from email.mime.text import MIMEText
from typing import List, Type, TypeVar, Callable

T = TypeVar("T")


class FFEmailSender:
    def __init__(self, email_address: str, email_password: str, smtp_server: str = "smtp.gmail.com",
                 smtp_port: int = 587):
        self.email_address = email_address
        self.email_password = email_password
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port

    def send_email(self, to_emails: List[str], subject: str, message: str):
        msg = MIMEText(message)
        msg['Subject'] = subject
        msg['From'] = self.email_address
        msg['To'] = ", ".join(to_emails)

        try:
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.email_address, self.email_password)
                server.sendmail(self.email_address, to_emails, msg.as_string())
                print("Email sent successfully.")
        except Exception as e:
            print(f"Failed to send email: {e}")


# def inject_email_sender(email_sender: EmailSender) -> Callable[[Type[T]], Type[T]]:
#     def decorator(cls: Type[T]) -> Type[T]:
#         setattr(cls, 'email_sender', email_sender)
#         return cls
#
#     return decorator

# example usage
# email_sender_instance = FFEmailSender(
#     email_address="<EMAIL>",
#     email_password="zwcukggmopcokzzp"
# )
#
# # Decorate the class with the EmailSender instance
# @inject_email_sender(email_sender=email_sender_instance)
# class ExampleClass:
#     def alert(self, subject, message):
#         recipients = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
#         self.email_sender.send_email(recipients, subject, message)
#
#
# # Use the decorated class
# example = ExampleClass()
# example.alert("Archelon Alert", "Archelon encountered an issue. Please check immediately.")


class ArchelonHighLow:

    def __init__(self):

        self.last_20_highest = None
        self.last_20_lowest = None
        self.last_10_highest = None
        self.last_10_lowest = None

    def update_high_low(self, data):

        self.last_20_highest = max(data.high[-20:])
        self.last_20_lowest = min(data.low[-20:])
        self.last_10_highest = max(data.high[-10:])
        self.last_10_lowest = min(data.low[-10:])

from common.enums.decision import Decision
from common.enums.exit_reason import ExitReason
from common.enums.position import Position


class PositionReporter:
    def __init__(self):
        self.entry_price = 0.0
        self.decision = Decision.do_nothing
        self.position = Position.notr
        self.exit_reason = ExitReason.nothing

    def reset(self):
        self.entry_price = 0.0
        self.position = Position.notr
        self.exit_reason = ExitReason.nothing

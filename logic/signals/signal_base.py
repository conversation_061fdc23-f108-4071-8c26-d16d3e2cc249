from abc import ABC, abstractmethod
import pandas as pd

from common.enums.position import Position
from common.enums.decision import Decision


class SignalBase(ABC):
    """
    Base class for all trading signals.
    Provides a common interface and structure for signal implementation.
    """

    def __init__(self, logger=None):
        """
        Initialize the signal with optional logger.

        Args:
            logger: Logger instance for logging signal activities
        """
        self.logger = logger

    # @abstractmethod
    # def run_signal(self, current_state: dict) -> Decision:
    #     """
    #     Main method to run the signal logic based on current position.
    #
    #     Args:
    #         current_position: Current position state (long, short, or notr)
    #
    #     Returns:
    #         Decision: Trading decision to take (buy, sell, do_nothing, market_exit, pyramid)
    #         :param current_state:
    #     """
    #     return Decision.do_nothing

    # @abstractmethod
    # def not_in_position_loop(self, current_price) -> Decision:
    #     """
    #     Logic to execute when not in a position.
    #
    #     Returns:
    #         Decision: Trading decision to take
    #     """
    #     return Decision.do_nothing
    #
    # @abstractmethod
    # def in_position_loop(self, current_price) -> Decision:
    #     """
    #     Logic to execute when in a position.
    #
    #     Returns:
    #         Decision: Trading decision to take
    #     """
    #     return Decision.do_nothing

from typing import Tuple

import pandas as pd
import pandas_ta as ta

from common.enums.decision import Decision
from common.enums.position import Position
from logic.data_containers.archelon_high_low import ArchelonHighLow
from logic.data_containers.archelon_pyramid import ArchelonPyramid
from logic.data_containers.trader_parameters import TraderParameters
from logic.data_containers.trader_state import TradeState
from logic.signals.signal_base import SignalBase


class Archelon(SignalBase):
    def __init__(self, logger, trade_params: TraderParameters):
        super().__init__(logger)
        self.archelon_amount = 0.0
        self.active_stop_loss = 0.0
        self.logger = logger
        self.trade_params = trade_params

        self.atr_value = 0.0
        self.amount = 0.0
        self.archelon_high_low = ArchelonHighLow()
        self.archelon_pyramid = ArchelonPyramid(logger)

    def run_signal(self, current_state: TradeState, candle_data) -> Decision:
        current_position = current_state.archelon_state.get_position()

        if current_position == Position.notr:
            return self.not_in_position_loop(current_state)
        else:
            return self.in_position_loop(current_state)

    def not_in_position_loop(self, current_state: TradeState):
        decision = Decision.do_nothing
        current_price = current_state.current_price
        current_archelon_position = current_state.archelon_state.get_position()
        position_str = 'LONG' if current_archelon_position == Position.long else 'SHORT' if current_archelon_position == Position.short else 'NONE'

        current_trade_position = current_state.current_active_position
        exit_condition, stop_price = self.check_position_exit(current_trade_position, current_price)
        if exit_condition == Decision.pyramid_exit:
            self.logger.info(f"[Archelon] Pyramid exit not in position loop {self.archelon_pyramid.pyramid_count} triggered - "
                                 f"Price: {current_price:.6f}, Stop: {stop_price}, Position: {position_str}")
            return Decision.pyramid_exit

        if current_price >= self.archelon_high_low.last_20_highest:
            return Decision.buy

        elif current_price <= self.archelon_high_low.last_20_lowest:
            return Decision.sell
        else:
            pyramid_decision = self.archelon_pyramid.check_pyramid(current_state)
            if pyramid_decision == Decision.pyramid:
                next_entry = self.archelon_pyramid.get_pyramid_entry()
                next_stop = self.archelon_pyramid.pyramid_stop_losses[self.archelon_pyramid.pyramid_count]
                self.logger.info(f"[Archelon] Pyramid level {self.archelon_pyramid.pyramid_count + 1} triggered - "
                                 f"Price: {current_price:.6f}, Next Entry: {next_entry:.6f}, Next Stop: {next_stop:.6f}, Position: {position_str}")
                return Decision.pyramid

            return decision

    def in_position_loop(self, current_state: TradeState):
        current_price = current_state.current_price
        current_position = current_state.archelon_state.get_position()
        position_str = 'LONG' if current_position == Position.long else 'SHORT' if current_position == Position.short else 'NONE'

        exit_condition, stop_price = self.check_position_exit(current_position, current_price)
        if exit_condition == Decision.market_exit:
            self.logger.info(
                f"[Archelon] Market exit triggered in position loop - Price: {current_price:.6f}, Stop Loss: {stop_price:.6f}, Position: {position_str}")
            return Decision.market_exit
        elif exit_condition == Decision.pyramid_exit:
            self.logger.info(
                f"[Archelon] Pyramid exit triggered - Price: {current_price:.6f}, Stop Loss: {stop_price:.6f}, Position: {position_str}")
            return Decision.pyramid_exit
        else:
            pyramid_decision = self.archelon_pyramid.check_pyramid(current_state)
            if pyramid_decision == Decision.pyramid:
                next_entry = self.archelon_pyramid.get_pyramid_entry()
                next_stop = self.archelon_pyramid.pyramid_stop_losses[self.archelon_pyramid.pyramid_count]
                self.logger.info(f"[Archelon] Pyramid level {self.archelon_pyramid.pyramid_count + 1} triggered - "
                                 f"Price: {current_price:.6f}, Next Entry: {next_entry:.6f}, Next Stop: {next_stop:.6f}, Position: {position_str}")
                return Decision.pyramid
        return Decision.do_nothing

    def calculate_entry_stop_loss(self, current_price: float, decision: Decision, atr_stop_multiplier: float = 2.0) -> float:
        entry_stop_loss_value = 0
        if decision == Decision.buy:
            entry_stop_loss_value = float(current_price - (atr_stop_multiplier * self.atr_value))
        elif decision == Decision.sell:
            entry_stop_loss_value = float(current_price + (atr_stop_multiplier * self.atr_value))
        return entry_stop_loss_value

    def init_archelon(self, candle_data: pd.DataFrame, current_state: TradeState, atr_multiplier: float = 2):
        current_state.atr_value = self.calculate_atr(current_state.archelon_state.get_position(), candle_data)
        self.archelon_high_low.update_high_low(candle_data)
        current_state.amount = self.calculate_entry_amount(atr_multiplier)
        return current_state

    def calculate_atr(self, current_position: Position, candle_data: pd.DataFrame, window=20) -> float:
        if current_position != Position.notr:
            #  this line enable us to save first atr value when we first open the position
            # so we can open same amount as a pyramid
            # no update
            # this line makes the code debuggable do not delete
            return 0.0
        else:
            series = candle_data.sort_values(by="time", ascending=True)
            indicator = ta.atr(high=series["high"], low=series["low"], close=series["close"], length=window,
                               mamode="EMA")
            values = indicator.tail(2).iloc[1]
            self.atr_value = values
            self.logger.info(f"[Archelon] atr_value: {self.atr_value}")
            return self.atr_value

    def calculate_entry_amount(self, atr_multiplier) -> float:
        self.amount = float(self.trade_params.amount_from_config * (1 / (self.atr_value * atr_multiplier)))
        return self.amount

    def check_position_exit(self, position: Position, current_price) -> Tuple[Decision, float]:
        active_stop_loss = self.archelon_pyramid.get_pyramid_stop_loss()

        if position == Position.long:
            if active_stop_loss:
                cond_b = bool(current_price < active_stop_loss)
                if cond_b:
                    reason = ' pyramid stop loss'
                    self.logger.info(f"[Archelon] Long exit condition met - Price: {current_price:.6f}, "
                                     f"Stop Loss: {active_stop_loss:.6f}, Reason: {reason}")
                    return Decision.pyramid_exit, active_stop_loss

            cond_a = bool(current_price < self.archelon_high_low.last_10_lowest)
            if cond_a:
                reason = '10-period low'
                self.logger.info(
                    f"[Archelon] Long exit condition met - Price: {current_price:.6f} 10-low: "
                    f"{self.archelon_high_low.last_10_lowest:.6f}, Reason: {reason}"
                )
                return Decision.market_exit, self.archelon_high_low.last_10_lowest

        elif position == Position.short:
            if active_stop_loss:
                cond_b = bool(current_price > active_stop_loss)
                if cond_b:
                    reason = 'pyramid stop loss'
                    self.logger.info(f"[Archelon] Short exit condition met - Price: {current_price:.6f}, "
                                     f"Stop Loss: {active_stop_loss:.6f}, Reason: {reason}")
                    return Decision.pyramid_exit, active_stop_loss

            cond_a = bool(current_price > self.archelon_high_low.last_10_highest)
            if cond_a:
                reason = '10-period high'
                self.logger.info(f"[Archelon] Short exit condition met - Price: {current_price:.6f}, "
                                  f"10-high: {self.archelon_high_low.last_10_highest:.6f} Reason: {reason}")
                return Decision.market_exit, self.archelon_high_low.last_10_highest

        return Decision.do_nothing, 0

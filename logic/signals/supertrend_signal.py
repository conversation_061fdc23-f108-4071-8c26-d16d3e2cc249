import numpy as np
import pandas as pd

from common.enums.decision import Decision
from logic.data_containers.trader_parameters import TraderParameters
from logic.data_containers.trader_state import TradeState
from logic.signals.signal_base import SignalBase


class SuperTrendSignal(SignalBase):
    source_type = "hl2"

    def __init__(self, trade_params: TraderParameters, logger):
        super().__init__()
        self.trade_params = trade_params
        self.supertrend_decision = None
        self.last_trend = None
        self.logger = logger

    def run_signal(self, candle_data: pd.DataFrame, trade_state: TradeState, trade_params: TraderParameters) -> Decision:

        candle_data = self.calculate_supertrend(candle_data, trade_params.supertrend_atr)

        # 'Supertrend_bool' column holds boolean: True=uptrend, False=downtrend
        current_trend_bool = candle_data['Supertrend_bool'].iloc[-1]

        if current_trend_bool:
            decision = Decision.buy
        else:
            decision = Decision.sell

        self.supertrend_decision = decision

        self.logger.debug(
            f"[SuperTrend] Decision: {decision}, Current trend: {'Up' if current_trend_bool else 'Down'}")
        # log last_trend dict if available
        if self.last_trend:
            self.logger.debug(f"[SuperTrend] Last trend: {self.last_trend}")

        return decision

    def check_wick(self, trade_state: TradeState) -> Decision:
        # Store logger reference if available
        if hasattr(trade_state, 'logger') and trade_state.logger is not None:
            self.logger = trade_state.logger

        ret_val = Decision.do_nothing
        current_price = trade_state.current_price
        if "upper" in (self.last_trend or {}):
            upper = self.last_trend["upper"]
            if current_price > upper:
                ret_val = Decision.buy
            else:
                ret_val = Decision.sell

            self.logger.debug(
                f"[SuperTrend] check_wick - Price: {current_price:.6f}, Upper band: {upper:.6f}, Decision: {ret_val}")

        elif "lower" in (self.last_trend or {}):
            lower = self.last_trend["lower"]
            if current_price < lower:
                ret_val = Decision.sell
            else:
                ret_val = Decision.buy

            self.logger.debug(
                f"[SuperTrend] check_wick - Price: {current_price:.6f}, Lower band: {lower:.6f}, Decision: {ret_val}")
        else:
            self.logger.warning("[SuperTrend] check_wick - No trend data available")

        return ret_val

    @staticmethod
    def _rma(series: pd.Series, window: int) -> pd.Series:
        """
        EMA-based moving average (RMA) for ATR smoothing.
        """
        alpha = 1 / window
        return series.ewm(alpha=alpha, adjust=False).mean()

    def calculate_supertrend(self, df: pd.DataFrame, supertrend_atr: float = 3.0, atr_window: int = 20, ):
        """
        Calculate SuperTrend using EMA-based ATR (RMA) per the provided calc_supertrend logic.
        Assumes df has columns: 'high', 'low', 'close'. Returns df with added columns:
          - 'Supertrend_value': the stop line value at each row (lower stop if uptrend, upper stop if downtrend)
          - 'Supertrend_bool': True for uptrend, False for downtrend
          - 'long_stop' and 'short_stop' if you want to keep them as columns (optional)
        Also updates self.last_trend as {'lower': val} or {'upper': val}.
        """
        df = df.copy().reset_index(drop=True)
        required_cols = {'high', 'low', 'close'}
        if not required_cols.issubset(df.columns.str.lower()):
            # ensure lowercase access
            raise ValueError(f"DataFrame must contain columns 'high', 'low', 'close'")

        # Work with lowercase column names
        # Compute hl2
        hl2 = (df['high'] + df['low']) / 2.0

        # EMA-based ATR
        prev_close = df['close'].shift(1)
        tr1 = df['high'] - df['low']
        tr2 = (df['high'] - prev_close).abs()
        tr3 = (df['low'] - prev_close).abs()
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr_rma = self._rma(tr, atr_window)

        # Basic stops
        long_stop = hl2 - supertrend_atr * atr_rma
        short_stop = hl2 + supertrend_atr * atr_rma

        # Prepare lists
        long_stop_final = [np.nan]  # first row
        short_stop_final = [np.nan]
        dir_list = [1]  # start with uptrend by default: 1=long/uptrend, -1=short/downtrend

        for i in range(1, len(df)):
            prev_long = long_stop_final[-1]
            prev_short = short_stop_final[-1]
            prev_dir = dir_list[-1]

            # Compute long_stop_final[i]
            if not np.isnan(prev_long):
                if df['low'].iloc[i - 1] > prev_long:
                    long_val = max(long_stop.iloc[i], prev_long)
                else:
                    long_val = long_stop.iloc[i]
            else:
                long_val = long_stop.iloc[i]
            long_stop_final.append(long_val)

            # Compute short_stop_final[i]
            if not np.isnan(prev_short):
                if df['high'].iloc[i - 1] < prev_short:
                    short_val = min(short_stop.iloc[i], prev_short)
                else:
                    short_val = short_stop.iloc[i]
            else:
                short_val = short_stop.iloc[i]
            short_stop_final.append(short_val)

            # Determine direction
            if prev_dir == -1 and df['high'].iloc[i] > prev_short:
                dir_now = 1
            elif prev_dir == 1 and df['low'].iloc[i] < prev_long:
                dir_now = -1
            else:
                dir_now = prev_dir
            dir_list.append(dir_now)

        # Build columns in df
        df['long_stop'] = long_stop_final
        df['short_stop'] = short_stop_final
        # Supertrend_value: the stop line to follow
        supertrend_value = []
        for i, d in enumerate(dir_list):
            if d == 1:
                supertrend_value.append(long_stop_final[i])
            else:
                supertrend_value.append(short_stop_final[i])
        df['Supertrend_value'] = supertrend_value
        # Boolean trend: True = uptrend (long), False = downtrend (short)
        df['Supertrend_bool'] = [d == 1 for d in dir_list]
        # Optional: store direction as string
        df['Trend_state'] = ['long' if d == 1 else 'short' for d in dir_list]

        # Update self.last_trend for the final row
        if len(df) > 0:
            final_dir = dir_list[-1]
            if final_dir == 1:
                # uptrend: use lower stop
                self.last_trend = {'lower': long_stop_final[-1]}
            else:
                # downtrend: use upper stop
                self.last_trend = {'upper': short_stop_final[-1]}

        return df

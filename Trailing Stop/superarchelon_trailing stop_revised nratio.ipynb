{"cells": [{"cell_type": "code", "id": "7301136b", "metadata": {"ExecuteTime": {"end_time": "2025-08-18T08:59:10.917074Z", "start_time": "2025-08-18T08:59:07.798351Z"}}, "source": ["# data<PERSON>i kaydeden kod\n", "import traceback\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from binance.client import Client\n", "import time\n", "from datetime import datetime\n", "\n", "\n", "class BacktestMeta:\n", "    def __init__(self):\n", "        # Tek tip parametre ile birden fazla coin eklemek için:\n", "        default_params = {\n", "            'INTERVALS': ['15m'],\n", "            'ATR_WINDOWS': [20],\n", "            'ATR_MULTIPLIERS': [1.5],\n", "            'ATR_MULTIPLIERS_ST': [3.5],\n", "            'STOP_MULTIPLIERS': [1.5],\n", "            'N_RATIOS': [(1.00, 0.00)],\n", "            'HIGHLOW_WINDOWS': [20],\n", "            'EXIT_PARAMS': [(40, -20, 60)],\n", "        }\n", "        coin_list = ['IMX']  # <PERSON><PERSON><PERSON> istediğin coinleri yazabilirsin\n", "\n", "        self.PARITE_PARAMETRELERI = {}\n", "        for coin in coin_list:\n", "            self.PARITE_PARAMETRELERI[coin] = default_params.copy()\n", "\n", "        # <PERSON><PERSON><PERSON> bazı coinler için özel parametre istiyo<PERSON>n, eski <PERSON> ekleyebilirsin:\n", "        # self.PARITE_PARAMETRELERI['TRX'] = {...}\n", "\n", "        self.COINS = list(self.PARITE_PARAMETRELERI.keys())\n", "        self.start_dt = datetime(2024, 1, 1, 0, 0, 0)\n", "        self.end_dt = datetime(2025, 12, 31, 23, 59, 59)\n", "        self.results = []\n", "        self.client = Client()\n", "        # Trading state variables\n", "        self.capital = None\n", "        self.balance = None\n", "        self.state = None\n", "        self.entry_price = None\n", "        self.entry_atr = None\n", "        self.pyramid = None\n", "        self.pyramid_entries = None\n", "        self.pyramid_amounts = None\n", "        self.max_price_during_trade = None\n", "        self.min_price_during_trade = None\n", "        self.max_pyramid = 4\n", "        self.commission_rate = 0.00045\n", "        self.cikis_sebebi = None\n", "        self.block_reason = None\n", "        # Tracking lists for simulation\n", "        self.states = []\n", "        self.capitals = []\n", "        self.balances = []\n", "        self.amounts = []\n", "        self.entry_prices = []\n", "        self.pyramids = []\n", "        self.exit_reasons = []\n", "        self.exit_prices = []\n", "        self.entry_block_reasons = []\n", "        self.profits = []\n", "        self.cumulative_profits = []\n", "        self.commissions = []\n", "        self.trade_profits = []\n", "        self.n_ratio_list = []\n", "        self.max_possible_profits = []\n", "        self.pyramid_entry_commissions = []  # Track entry commissions per pyramid entry\n", "        self.pyramid_exit_commissions = []  # Track exit commissions per pyramid exit\n", "        # Ensure state variables are always initialized to valid types\n", "        self.balance = 0.0\n", "        self.pyramid_entries = []\n", "        self.pyramid_amounts = []\n", "        self.first_entry_price = None  # EKLENDİ: <PERSON>lk piramit giriş fiyatı\n", "\n", "    def rma(self, series, window):\n", "        alpha = 1 / window\n", "        return series.ewm(alpha=alpha, adjust=False).mean()\n", "\n", "    def calc_atr_rma(self, df, window):\n", "        prev_close = df['Close'].shift(1)\n", "        tr1 = df['High'] - df['Low']\n", "        tr2 = (df['High'] - prev_close).abs()\n", "        tr3 = (df['Low'] - prev_close).abs()\n", "        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)\n", "        return self.rma(tr, window)\n", "\n", "    def calc_supertrend(self, df, atr_window, multiplier):\n", "        hl2 = (df['High'] + df['Low']) / 2\n", "        atr = self.calc_atr_rma(df, atr_window)\n", "        upperband = hl2 + multiplier * atr\n", "        lowerband = hl2 - multiplier * atr\n", "\n", "        final_upperband = upperband.copy()\n", "        final_lowerband = lowerband.copy()\n", "        supertrend = [np.nan] * len(df)\n", "        supertrend_state = ['neutral'] * len(df)\n", "\n", "        for i in range(len(df)):\n", "            if i == 0:\n", "                supertrend[i] = final_upperband.iloc[i]\n", "                if df['High'].iloc[i] < supertrend[i]:\n", "                    supertrend_state[i] = 'short'\n", "                elif df['Low'].iloc[i] > supertrend[i]:\n", "                    supertrend_state[i] = 'long'\n", "                else:\n", "                    supertrend_state[i] = 'short' if df['Close'].iloc[i] < supertrend[i] else 'long'\n", "                continue\n", "\n", "            if (upperband.iloc[i] < final_upperband.iloc[i - 1]) or (\n", "                    df['High'].iloc[i - 1] > final_upperband.iloc[i - 1]):\n", "                final_upperband.iloc[i] = upperband.iloc[i]\n", "            else:\n", "                final_upperband.iloc[i] = final_upperband.iloc[i - 1]\n", "\n", "            if (lowerband.iloc[i] > final_lowerband.iloc[i - 1]) or (\n", "                    df['Low'].iloc[i - 1] < final_lowerband.iloc[i - 1]):\n", "                final_lowerband.iloc[i] = lowerband.iloc[i]\n", "            else:\n", "                final_lowerband.iloc[i] = final_lowerband.iloc[i - 1]\n", "\n", "            prev_state = supertrend_state[i - 1]\n", "            if prev_state == 'long':\n", "                if df['Low'].iloc[i] < final_lowerband.iloc[i]:\n", "                    supertrend[i] = final_upperband.iloc[i]\n", "                    supertrend_state[i] = 'short'\n", "                else:\n", "                    supertrend[i] = final_lowerband.iloc[i]\n", "                    supertrend_state[i] = 'long'\n", "            elif prev_state == 'short':\n", "                if df['High'].iloc[i] > final_upperband.iloc[i]:\n", "                    supertrend[i] = final_lowerband.iloc[i]\n", "                    supertrend_state[i] = 'long'\n", "                else:\n", "                    supertrend[i] = final_upperband.iloc[i]\n", "                    supertrend_state[i] = 'short'\n", "            else:\n", "                supertrend[i] = final_upperband.iloc[i]\n", "                if df['High'].iloc[i] < supertrend[i]:\n", "                    supertrend_state[i] = 'short'\n", "                elif df['Low'].iloc[i] > supertrend[i]:\n", "                    supertrend_state[i] = 'long'\n", "                else:\n", "                    supertrend_state[i] = 'short' if df['Close'].iloc[i] < supertrend[i] else 'long'\n", "\n", "        df['supertrend'] = pd.Series(supertrend, index=df.index)\n", "        df['supertrend_state'] = pd.Series(supertrend_state, index=df.index)\n", "        df['supertrend_upperband'] = final_upperband\n", "        df['supertrend_lowerband'] = final_lowerband\n", "\n", "        return df\n", "\n", "    def calc_turtle_state(self, df, donchian_period=20):\n", "        df['donchian_high'] = df['High'].rolling(window=donchian_period).max().shift(1)\n", "        df['donchian_low'] = df['Low'].rolling(window=donchian_period).min().shift(1)\n", "        df['turtle_state'] = 'neutral'\n", "        for i in range(1, len(df)):\n", "            prev_state = df['turtle_state'].iloc[i - 1]\n", "            if prev_state == 'neutral':\n", "                if df['High'].iloc[i] > df['donchian_high'].iloc[i]:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'long'\n", "                elif df['Low'].iloc[i] < df['donchian_low'].iloc[i]:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'short'\n", "                else:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'neutral'\n", "            elif prev_state == 'long':\n", "                exit_period = donchian_period // 2\n", "                exit_low = df['Low'].iloc[max(0, i - exit_period):i].min()\n", "                full_switch_low = df['Low'].iloc[max(0, i - donchian_period):i].min()\n", "                if df['Low'].iloc[i] <= full_switch_low:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'short'\n", "                elif df['Low'].iloc[i] <= exit_low:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'neutral'\n", "                else:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'long'\n", "            elif prev_state == 'short':\n", "                exit_period = donchian_period // 2\n", "                exit_high = df['High'].iloc[max(0, i - exit_period):i].max()\n", "                full_switch_high = df['High'].iloc[max(0, i - donchian_period):i].max()\n", "                if df['High'].iloc[i] >= full_switch_high:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'long'\n", "                elif df['High'].iloc[i] >= exit_high:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'neutral'\n", "                else:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'short'\n", "        return df\n", "\n", "    def check_indicator_switches(self, df, i):\n", "        turtle_switch = False\n", "        supertrend_switch = False\n", "        if i > 0:\n", "            if df['turtle_state'].iloc[i] != df['turtle_state'].iloc[i - 1]:\n", "                turtle_switch = True\n", "            if df['supertrend_state'].iloc[i] != df['supertrend_state'].iloc[i - 1]:\n", "                supertrend_switch = True\n", "        return turtle_switch, supertrend_switch\n", "\n", "    def fetch_klines(self, symbol, interval, start_time, end_time):\n", "        all_klines = []\n", "        limit = 1000\n", "        start_ts = int(start_time.timestamp() * 1000)\n", "        end_ts = int(end_time.timestamp() * 1000)\n", "        while start_ts < end_ts:\n", "            klines = self.client.futures_klines(\n", "                symbol=symbol,\n", "                interval=interval,\n", "                startTime=start_ts,\n", "                endTime=end_ts,\n", "                limit=limit\n", "            )\n", "            if not klines:\n", "                break\n", "            all_klines.extend(klines)\n", "            last_open_time = klines[-1][0]\n", "            start_ts = last_open_time + 1\n", "            time.sleep(0.2)\n", "        columns = [\n", "            'Open Time', 'Open', 'High', 'Low', 'Close', 'Volume',\n", "            'Close Time', 'Quote Asset Volume', 'Number of Trades',\n", "            'Taker Buy Base Asset Volume', 'Taker Buy Quote Asset Volume', 'Ignore'\n", "        ]\n", "        df = pd.DataFrame(all_klines, columns=columns)\n", "        df['Open Time'] = pd.to_datetime(df['Open Time'], unit='ms')\n", "        df['Close Time'] = pd.to_datetime(df['Close Time'], unit='ms')\n", "        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:\n", "            df[col] = df[col].astype(float)\n", "        return df\n", "\n", "    def _enter_position(self, position_type, entry_price, amount):\n", "        commission = amount * entry_price * self.commission_rate\n", "        total_cost = amount * entry_price + commission\n", "        if self.capital >= total_cost:\n", "            if position_type == 'long':\n", "                new_balance = amount\n", "                new_capital = self.capital - total_cost\n", "            else:\n", "                new_balance = -amount\n", "                new_capital = self.capital + amount * entry_price - commission\n", "            self.pyramid_entry_commissions = [commission]\n", "            return new_capital, new_balance\n", "        else:\n", "            return self.capital, 0\n", "\n", "    def _exit_position(self, position_type, exit_price):\n", "        bal = self.balance if self.balance is not None else 0.0\n", "        entries = self.pyramid_entries if self.pyramid_entries is not None else []\n", "        amounts = self.pyramid_amounts if self.pyramid_amounts is not None else []\n", "        entry_commissions = self.pyramid_entry_commissions if hasattr(self, 'pyramid_entry_commissions') else []\n", "        exit_commissions = []\n", "        per_entry_profits = []\n", "        self.exit_price = exit_price\n", "        if position_type == 'long':\n", "            new_capital = self.capital + bal * exit_price\n", "            for ep, am in zip(entries, amounts):\n", "                exit_comm = am * exit_price * self.commission_rate\n", "                per_entry_profits.append((exit_price - ep) * am)\n", "                exit_commissions.append(exit_comm)\n", "            realized_profit = sum(per_entry_profits)\n", "            max_profit = sum([(self.max_price_during_trade - ep) * am for ep, am in zip(entries, amounts)])\n", "        else:\n", "            new_capital = self.capital - abs(bal) * exit_price\n", "            for ep, am in zip(entries, amounts):\n", "                exit_comm = am * exit_price * self.commission_rate\n", "                per_entry_profits.append((ep - exit_price) * am)\n", "                exit_commissions.append(exit_comm)\n", "            realized_profit = sum(per_entry_profits)\n", "            max_profit = sum([(ep - self.min_price_during_trade) * am for ep, am in zip(entries, amounts)])\n", "        self.pyramid_exit_commissions = exit_commissions\n", "        total_entry_comm = sum(entry_commissions)\n", "        total_exit_comm = sum(exit_commissions)\n", "        new_capital = new_capital - total_entry_comm - total_exit_comm\n", "        return new_capital, realized_profit, max_profit\n", "\n", "    def _add_pyramid_position(self, position_type, pyramid_trigger, amount):\n", "        cap = self.capital if self.capital is not None else 0.0\n", "        entries = self.pyramid_entries if self.pyramid_entries is not None else []\n", "        amounts = self.pyramid_amounts if self.pyramid_amounts is not None else []\n", "        commission = amount * pyramid_trigger * self.commission_rate\n", "        total_cost = amount * pyramid_trigger + commission\n", "        if cap >= total_cost:\n", "            if position_type == 'long':\n", "                new_balance = amount\n", "                new_capital = cap - total_cost\n", "            else:\n", "                new_balance = -amount\n", "                new_capital = cap + amount * pyramid_trigger - commission\n", "            new_pyramid_entries = entries + [pyramid_trigger]\n", "            new_pyramid_amounts = amounts + [amount]\n", "            if hasattr(self, 'pyramid_entry_commissions') and self.pyramid_entry_commissions:\n", "                new_pyramid_entry_commissions = self.pyramid_entry_commissions + [commission]\n", "            else:\n", "                new_pyramid_entry_commissions = [commission]\n", "            self.pyramid_entry_commissions = new_pyramid_entry_commissions\n", "            return new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts\n", "        else:\n", "            return cap, 0, 0, entries, amounts\n", "\n", "    def _check_stop_loss(self, position_type, entry_price, entry_atr, stop_mult, current_price):\n", "        if position_type == 'long':\n", "            stop_price = entry_price - stop_mult * entry_atr\n", "            stop_triggered = current_price < stop_price\n", "        else:\n", "            stop_price = entry_price + stop_mult * entry_atr\n", "            stop_triggered = current_price > stop_price\n", "        return stop_triggered, stop_price\n", "\n", "    def _check_exit_conditions(self, position_type, turtle_price, supertrend_price):\n", "        if position_type == 'long':\n", "            return turtle_price if turtle_price > supertrend_price else supertrend_price\n", "        else:\n", "            return turtle_price if turtle_price < supertrend_price else supertrend_price\n", "\n", "    def _check_pyramid_conditions(self, position_type, current_price, prev_entry_price, entry_atr):\n", "        if position_type == 'long':\n", "            pyramid_trigger_price = prev_entry_price + 0.5 * entry_atr\n", "            pyramid_triggered = current_price > pyramid_trigger_price\n", "        else:\n", "            pyramid_trigger_price = prev_entry_price - 0.5 * entry_atr\n", "            pyramid_triggered = current_price < pyramid_trigger_price\n", "        return pyramid_triggered, pyramid_trigger_price\n", "\n", "    def _execute_pyramid_entry(self, position_type, current_price, prev_entry_price, entry_atr, amount):\n", "        pyr = self.pyramid if self.pyramid is not None else 0\n", "        max_pyr = self.max_pyramid if self.max_pyramid is not None else 0\n", "        cap = self.capital if self.capital is not None else 0.0\n", "        entries = self.pyramid_entries if self.pyramid_entries is not None else []\n", "        amounts = self.pyramid_amounts if self.pyramid_amounts is not None else []\n", "        if pyr >= max_pyr:\n", "            return cap, 0, 0, entries, amounts, pyr, prev_entry_price, False\n", "        pyramid_triggered, pyramid_trigger_price = self._check_pyramid_conditions(\n", "            position_type, current_price, prev_entry_price, entry_atr\n", "        )\n", "        if pyramid_triggered:\n", "            new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts = \\\n", "                self._add_pyramid_position(position_type, pyramid_trigger_price, amount)\n", "            if new_balance != 0:\n", "                new_pyramid = pyr + 1\n", "                new_entry_price = pyramid_trigger_price\n", "                return new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts, new_pyramid, new_entry_price, True\n", "            else:\n", "                return cap, 0, entries, amounts, pyr, prev_entry_price, False\n", "        else:\n", "            return cap, 0, entries, amounts, pyr, prev_entry_price, False\n", "\n", "    def run_backtest(self):\n", "        for coin in self.COINS:\n", "            params = self.PARITE_PARAMETRELERI[coin]\n", "            for interval in params['INTERVALS']:\n", "                for atr_window in params['ATR_WINDOWS']:\n", "                    for atr_mult, stop_mult in zip(params['ATR_MULTIPLIERS'], params['STOP_MULTIPLIERS']):\n", "                        for highlow_window in params['HIGHLOW_WINDOWS']:\n", "                            for atr_mult_st in params['ATR_MULTIPLIERS_ST']:\n", "                                for n_ratio_lower, n_ratio_upper in params['N_RATIOS']:\n", "                                    for max_profit_limit, threshold_value, profit_follow_distance in params['EXIT_PARAMS']:\n", "                                        try:\n", "                                            symbol = f\"{coin}USDT\"\n", "                                            print(\n", "                                                f\"Başlıyor: {symbol} {interval} ATRwin:{atr_window} ATRmult:{atr_mult} STOPmult:{stop_mult} HLwin:{highlow_window} PROFIT_FOLLOW_DISTANCE:{profit_follow_distance}\")\n", "                                            df = self.fetch_klines(symbol, interval, self.start_dt, self.end_dt)\n", "                                            # ...devamı aynı...\n", "                                            df['Previous Close'] = df['Close'].shift(1)\n", "                                            df['TR'] = df[['High', 'Low', 'Previous Close']].apply(\n", "                                                    lambda row: max(\n", "                                                    row['High'] - row['Low'],\n", "                                                    abs(row['High'] - row['Previous Close']),\n", "                                                    abs(row['Low'] - row['Previous Close'])\n", "                                                ), axis=1\n", "                                            )\n", "                                            df[f'ATR_EMA{atr_window}'] = df['TR'].shift(1).ewm(span=atr_window,\n", "                                                                                        adjust=False).mean()\n", "                                            df[f'Low_{highlow_window}h'] = df['Low'].shift(1).rolling(\n", "                                                window=highlow_window).min()\n", "                                            df[f'High_{highlow_window}h'] = df['High'].shift(1).rolling(\n", "                                                window=highlow_window).max()\n", "                                            exit_window = int(highlow_window / 2)\n", "                                            df[f'Low_{exit_window}h'] = df['Low'].shift(1).rolling(window=exit_window).min()\n", "                                            df[f'High_{exit_window}h'] = df['High'].shift(1).rolling(window=exit_window).max()\n", "                                            df = self.calc_supertrend(df, atr_window, atr_mult_st)\n", "                                            df = self.calc_turtle_state(df, donchian_period=highlow_window)\n", "                                            self._run_trading_simulation(\n", "                                                df, coin, interval, atr_window, atr_mult,\n", "                                                atr_mult_st, stop_mult, highlow_window, exit_window,\n", "                                                n_ratio_lower, n_ratio_upper, max_profit_limit, threshold_value, profit_follow_distance\n", "                                            )\n", "                                        except Exception as e:\n", "                                            traceback.print_exc()\n", "                                            print(\n", "                                                f\"HATA: {coin} {interval} {atr_window} {atr_mult} {atr_mult_st} {stop_mult} {highlow_window} -> {e}\")\n", "                                            continue\n", "        self._process_final_results()\n", "\n", "    def add_max_column(self, df):\n", "        # Yeni kolon: max_cum_profit_drawdown\n", "        max_cum = None\n", "        freeze = False\n", "        max_cum_list = []\n", "        for val in df['cumulative_profit']:\n", "            if max_cum is None or val > max_cum:\n", "                max_cum = val\n", "                freeze = False\n", "                max_cum_list.append(0)\n", "            elif val < max_cum:\n", "                freeze = True\n", "                max_cum_list.append(max_cum - val)\n", "            else:\n", "                max_cum_list.append(0)\n", "        return max_cum_list\n", "\n", "    def reset(self, new_capital):\n", "        self.entry_price = None\n", "        self.entry_atr = None\n", "        self.pyramid = 0\n", "        self.pyramid_entries = []\n", "        self.pyramid_amounts = []\n", "        self.pyramid_entry_commissions = []\n", "        self.pyramid_exit_commissions = []\n", "        self.max_price_during_trade = None\n", "        self.min_price_during_trade = None\n", "        self.capital = new_capital\n", "        self.balance = 0\n", "        self.state = 'neutral'\n", "        self.first_entry_price = None  # Pozisyon kapandı, sı<PERSON><PERSON><PERSON>a\n", "\n", "    def update_commissions_profit(self, i, realized_profit, max_profit):\n", "        entry_index = i - self.how_many_hours[i]\n", "        self.entry_commissions[entry_index] = sum(self.pyramid_entry_commissions)\n", "        self.exit_commissions[entry_index] = sum(self.pyramid_exit_commissions)\n", "        self.cumulative_profits[entry_index] = realized_profit - sum(\n", "            self.pyramid_entry_commissions) - sum(\n", "            self.exit_commissions)\n", "        self.trade_profits[entry_index] = realized_profit\n", "        self.max_possible_profits[entry_index] = max_profit\n", "\n", "    def _run_trading_simulation(self, df, coin, interval, atr_window, atr_mult, atr_mult_st,\n", "                            stop_mult, highlow_window, exit_window, n_ratio_lower, n_ratio_upper,\n", "                            max_profit_limit, threshold_value, profit_follow_distance):\n", "        initial_capital = 100000000\n", "        self.capital = initial_capital\n", "        self.balance = 0.0\n", "        self.state = 'neutral'\n", "        self.entry_price = None\n", "        self.entry_atr = None\n", "        self.pyramid = 0\n", "        self.n_ratio_list = []\n", "        self.max_price_during_trade = None\n", "        self.min_price_during_trade = None\n", "        self.cikis_sebebi = None\n", "        self.block_reason = None\n", "        self.states = []\n", "        self.capitals = []\n", "        self.balances = []\n", "        self.amounts = []\n", "        self.entry_prices = []\n", "        self.pyramids = []\n", "        self.exit_reasons = []\n", "        self.exit_prices = []\n", "        self.entry_block_reasons = []\n", "        self.profits = []\n", "        self.cumulative_profits = []\n", "        self.commissions = []\n", "        self.trade_profits = []\n", "        self.max_possible_profits = []\n", "        self.pyramid_entry_commissions = []\n", "        self.pyramid_exit_commissions = []\n", "        self.entry_commissions = []\n", "        self.exit_commissions = []\n", "        self.balance = 0.0\n", "        self.pyramid_entries = []\n", "        self.pyramid_amounts = []\n", "        self.how_many_hours = []\n", "        self.first_entry_price = None  # EKLENDİ: <PERSON>lk piramit giriş fiyatı\n", "\n", "        for i, row in df.iterrows():\n", "            high = row['High']\n", "            low = row['Low']\n", "            atr = row[f'ATR_EMA{atr_window}']\n", "            high_exit = row.get(f'High_{exit_window}h', np.nan)\n", "            low_exit = row.get(f'Low_{exit_window}h', np.nan)\n", "            st_up = row.get('supertrend_upperband', np.nan)\n", "            st_low = row.get('supertrend_lowerband', np.nan)\n", "            if self.state in ['long', 'short']:\n", "                entries = self.pyramid_entries if self.pyramid_entries else []\n", "                amounts = self.pyramid_amounts if self.pyramid_amounts else []\n", "                if self.state == 'long':\n", "                    self.max_price_during_trade = max(self.max_price_during_trade, row['High']) if self.max_price_during_trade is not None else row['High']\n", "                    current_max_profit = sum([(self.max_price_during_trade - ep) * am for ep, am in zip(entries, amounts)])\n", "                else:\n", "                    self.min_price_during_trade = min(self.min_price_during_trade, row['Low']) if self.min_price_during_trade is not None else row['Low']\n", "                    current_max_profit = sum([(ep - self.min_price_during_trade) * am for ep, am in zip(entries, amounts)])\n", "                self.max_possible_profits[-1] = current_max_profit\n", "            self.cikis_sebebi = None\n", "            self.exit_price = None\n", "            self.block_reason = None\n", "            realized_profit = 0\n", "            self.trade_profits.append(0)\n", "            self.cumulative_profits.append(0)\n", "            self.entry_commissions.append(0)\n", "            self.exit_commissions.append(0)\n", "            self.max_possible_profits.append(0)\n", "            self.n_ratio_list.append(None)\n", "\n", "            if self.state == 'neutral':\n", "                current_atr = atr\n", "            else:\n", "                current_atr = self.entry_atr\n", "\n", "            prev_state = self.state\n", "            prev_entry_price = self.entry_price\n", "\n", "            current_turtle_state = df['turtle_state'].iloc[i]\n", "            current_supertrend_state = df['supertrend_state'].iloc[i]\n", "\n", "            # n_ratio hesaplama ve sabitleme\n", "            if prev_state == 'neutral':\n", "                self.try_enter_position(row, atr_mult, atr_window, highlow_window, df, i, n_ratio_lower, n_ratio_upper)\n", "                self.how_many_hours.append(0)\n", "\n", "            if prev_state == 'long':\n", "                self.how_many_hours.append(self.how_many_hours[-1] + 1)\n", "                if self.max_price_during_trade is not None:\n", "                    self.max_price_during_trade = max(self.max_price_during_trade, high)\n", "                if self.min_price_during_trade is not None:\n", "                    self.min_price_during_trade = min(self.min_price_during_trade, low)\n", "                realized_profit = sum([(row['Close'] - ep) * am for ep, am in zip(self.pyramid_entries, self.pyramid_amounts)])\n", "                max_profit = sum([(self.max_price_during_trade - ep) * am for ep, am in zip(self.pyramid_entries, self.pyramid_amounts)])\n", "                \n", "                if max_profit < max_profit_limit:\n", "                    threshold = threshold_value\n", "                else:\n", "                    threshold = max_profit - profit_follow_distance\n", "\n", "                # Çık<PERSON>ş kontrolü\n", "                if realized_profit <= threshold:\n", "                    total_amount = sum(self.pyramid_amounts)\n", "                    avg_entry = sum([ep * am for ep, am in zip(self.pyramid_entries, self.pyramid_amounts)]) / total_amount if total_amount > 0 else self.entry_price\n", "                    exit_price = avg_entry + (threshold / total_amount)\n", "                    new_capital, realized_profit, max_profit = self._exit_position('long', exit_price)\n", "                    self.update_commissions_profit(i, realized_profit, max_profit)\n", "                    self.max_possible_profits[-1] = max_profit\n", "                    self.cikis_sebebi = f'<PERSON>r Eşiği Altı Çıkış ({threshold})'\n", "                    self.reset(new_capital)\n", "                    self.try_enter_position(row, atr_mult, atr_window, highlow_window, df, i, n_ratio_lower, n_ratio_upper)\n", "                    current_n_ratio = None\n", "                elif current_turtle_state in ['short', 'neutral'] and current_supertrend_state == 'short':\n", "                    final_exit_price = self._check_exit_conditions('long', low_exit, st_low)\n", "                    if final_exit_price:\n", "                        new_capital, realized_profit, max_profit = self._exit_position(\n", "                            'long', final_exit_price\n", "                        )\n", "                        self.update_commissions_profit(i, realized_profit, max_profit)\n", "                        self.max_possible_profits[-1] = max_profit  # Sadece <PERSON> gü<PERSON>lle          \n", "                        self.reset(new_capital)\n", "                        self.cikis_sebebi = f'Long Low{exit_window} Çıkışı'\n", "                        self.try_enter_position(\n", "                            row, atr_mult, atr_window, highlow_window, df, i, n_ratio_lower, n_ratio_upper\n", "                        )\n", "                        current_n_ratio = None\n", "                    else:\n", "                        self.cikis_sebebi = 'Pozisyon Açık'\n", "                elif self.pyramid < self.max_pyramid:\n", "                    piramit_yapildi = False\n", "                    # Ekle: <PERSON>d başındaki piramit listesini sakla\n", "                    base_entries = self.pyramid_entries.copy()\n", "                    base_amounts = self.pyramid_amounts.copy()\n", "                    base_commissions = self.pyramid_entry_commissions.copy()\n", "                    base_pyramid = self.pyramid\n", "                    base_entry_price = self.entry_price\n", "                    while self.pyramid < self.max_pyramid:\n", "                        new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts, new_pyramid, new_entry_price, pyramid_executed = \\\n", "                            self._execute_pyramid_entry('long', high, self.entry_price, self.entry_atr, amount)\n", "                        if pyramid_executed:\n", "                            self.capital = new_capital\n", "                            self.balance += new_balance\n", "                            # Sad<PERSON>e bir tane amount ekle!\n", "                            self.pyramid_entries = base_entries + [new_entry_price] * (new_pyramid - base_pyramid)\n", "                            self.pyramid_amounts = base_amounts + [amount] * (new_pyramid - base_pyramid)\n", "                            self.pyramid = new_pyramid\n", "                            self.entry_price = new_entry_price\n", "                            piramit_yapildi = True\n", "                        else:\n", "                            break\n", "                    self.cikis_sebebi = 'Long Pyramid' if pira<PERSON>_yapildi else 'Pozisyon Açık'\n", "\n", "            elif prev_state == 'short':\n", "                self.how_many_hours.append(self.how_many_hours[-1] + 1)\n", "                if self.max_price_during_trade is not None:\n", "                    self.max_price_during_trade = max(self.max_price_during_trade, high)\n", "                if self.min_price_during_trade is not None:\n", "                    self.min_price_during_trade = min(self.min_price_during_trade, low)\n", "                realized_profit = sum([(ep - row['Close']) * am for ep, am in zip(self.pyramid_entries, self.pyramid_amounts)])\n", "                # max_profit her adım<PERSON> g<PERSON>!\n", "                max_profit = sum([(ep - self.min_price_during_trade) * am for ep, am in zip(self.pyramid_entries, self.pyramid_amounts)])\n", "                # <PERSON><PERSON> <PERSON> kuralı\n", "                if max_profit < max_profit_limit:\n", "                    threshold = threshold_value\n", "                else:\n", "                    threshold = max_profit - profit_follow_distance\n", "\n", "                # Çık<PERSON>ş kontrolü\n", "                if realized_profit <= threshold:\n", "                    total_amount = sum(self.pyramid_amounts)\n", "                    avg_entry = sum([ep * am for ep, am in zip(self.pyramid_entries, self.pyramid_amounts)]) / total_amount if total_amount > 0 else self.entry_price\n", "                    exit_price = avg_entry - (threshold / total_amount)\n", "                    new_capital, realized_profit, max_profit = self._exit_position('short', exit_price)\n", "                    self.update_commissions_profit(i, realized_profit, max_profit)\n", "                    self.max_possible_profits[-1] = max_profit\n", "                    self.cikis_sebebi = f'<PERSON>r Eşiği Altı Çıkış ({threshold})'\n", "                    self.reset(new_capital)\n", "                    self.try_enter_position(row, atr_mult, atr_window, highlow_window, df, i, n_ratio_lower, n_ratio_upper)\n", "                    current_n_ratio = None\n", "\n", "                elif (not np.isnan(high_exit) and high > high_exit) and current_supertrend_state == 'long':\n", "                    final_exit_price = self._check_exit_conditions('short', high_exit, st_up)\n", "                    if final_exit_price:\n", "                        new_capital, realized_profit, max_profit = self._exit_position(\n", "                            'short', final_exit_price\n", "                        )\n", "                        self.update_commissions_profit(i, realized_profit, max_profit)\n", "                        self.max_possible_profits[-1] = max_profit  # Sadece ka<PERSON> g<PERSON>l\n", "                        self.reset(new_capital)\n", "                        self.cikis_sebebi = f'Short High{exit_window} Çıkışı'\n", "                        self.try_enter_position(\n", "                            row, atr_mult, atr_window, highlow_window, df, i, n_ratio_lower, n_ratio_upper\n", "                        )\n", "                        current_n_ratio = None\n", "                    else:\n", "                        self.cikis_sebebi = 'Pozisyon Açık'\n", "                elif self.pyramid < self.max_pyramid:\n", "                    piramit_yapildi = False\n", "                    # Ekle: <PERSON>d başındaki piramit listesini sakla\n", "                    base_entries = self.pyramid_entries.copy()\n", "                    base_amounts = self.pyramid_amounts.copy()\n", "                    base_commissions = self.pyramid_entry_commissions.copy()\n", "                    base_pyramid = self.pyramid\n", "                    base_entry_price = self.entry_price\n", "                    while self.pyramid < self.max_pyramid:\n", "                        new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts, new_pyramid, new_entry_price, pyramid_executed = \\\n", "                            self._execute_pyramid_entry('short', low, self.entry_price, self.entry_atr, amount)\n", "                        if pyramid_executed:\n", "                            self.capital = new_capital\n", "                            self.balance += new_balance\n", "                            # Sad<PERSON>e bir tane amount ekle!\n", "                            self.pyramid_entries = base_entries + [new_entry_price] * (new_pyramid - base_pyramid)\n", "                            self.pyramid_amounts = base_amounts + [amount] * (new_pyramid - base_pyramid)\n", "                            self.pyramid = new_pyramid\n", "                            self.entry_price = new_entry_price\n", "                            piramit_yapildi = True\n", "                        else:\n", "                            break\n", "                    self.cikis_sebebi = 'Short Pyramid' if piramit_yapildi else 'Pozisyon Açık'\n", "\n", "            self.profits.append(realized_profit)\n", "            self.exit_prices.append(self.exit_price)\n", "            self.entry_block_reasons.append(self.block_reason)\n", "            self.states.append(self.state)\n", "            self.capitals.append(self.capital)\n", "            self.balances.append(self.balance)\n", "            if self.state in ['long', 'short'] and self.pyramid_amounts:\n", "                amount = sum(self.pyramid_amounts)\n", "            else:\n", "                amount = 0\n", "            self.amounts.append(amount)\n", "            self.entry_prices.append(self.entry_price)\n", "            self.pyramids.append(self.pyramid)\n", "            self.exit_reasons.append(self.cikis_sebebi)\n", "\n", "        df['State'] = self.states\n", "        df['capital'] = self.capitals\n", "        df['balance'] = self.balances\n", "        df['amount'] = self.amounts\n", "        df['entry_price'] = self.entry_prices\n", "        df['pyramid'] = self.pyramids\n", "        df['exit_reason'] = self.exit_reasons\n", "        df['exit_price'] = self.exit_prices\n", "        df['commissions'] = [a + b for a, b in zip(self.entry_commissions, self.exit_commissions)]\n", "        df['entry_block_reason'] = self.entry_block_reasons\n", "        df['profit'] = self.trade_profits\n", "        df['entry_commissions'] = self.entry_commissions\n", "        df['exit_commissions'] = self.exit_commissions\n", "        df['profit_comission_included'] = df['profit'] - df['entry_commissions'] - df['exit_commissions']\n", "        df['cumulative_profit'] = df['profit_comission_included'].cumsum()\n", "        df['trade_profit'] = self.trade_profits\n", "        df['max_possible_profit'] = self.max_possible_profits\n", "        df['n_ratio'] = self.n_ratio_list\n", "        df['max_cum_profit_drawdown'] = self.add_max_column(df)\n", "\n", "        drop_cols = ['Volume', 'Close Time', 'Quote Asset Volume', 'Number of Trades', 'Taker Buy Base Asset Volume',\n", "                     'Taker Buy Quote Asset Volume', 'Ignore', 'Previous Close', 'capital', 'balance',\n", "                     'entry_block_reason', 'trade_profit']\n", "        for col in drop_cols:\n", "            if col in df.columns:\n", "                df.drop(col, axis=1, inplace=True)\n", "\n", "        print(\"\\nDataFrame Structure:\")\n", "        print(df.info())\n", "        print(\"\\nFirst 5 rows of DataFrame:\")\n", "        print(df.head())\n", "        print(\"\\nTurtle State Analysis:\")\n", "        print(df[['Open Time', 'Close', 'donchian_high', 'donchian_low', 'turtle_state', 'supertrend_state']].tail(10))\n", "        print(\"\\nTurtle State Distribution:\")\n", "        print(df['turtle_state'].value_counts())\n", "        print(\"\\nComparison with Supertrend:\")\n", "        state_comparison = pd.crosstab(df['turtle_state'], df['supertrend_state'])\n", "        print(state_comparison)\n", "\n", "        excel_file = f\"{coin}_{interval}_ATRW{atr_window}_ATRm{atr_mult}_ATRst{atr_mult_st}_STOPm{stop_mult}_HLW{highlow_window}_PROFITDIST{profit_follow_distance}.xlsx\"\n", "        with pd.ExcelWriter(excel_file, engine=\"xlsxwriter\") as writer:\n", "            df.to_excel(writer, index=False)\n", "            worksheet = writer.sheets[\"Sheet1\"]\n", "            worksheet.freeze_panes(1, 1)\n", "        print(f\"Kay<PERSON><PERSON><PERSON>: {excel_file}\")\n", "\n", "        df['year'] = df['Open Time'].dt.year\n", "        yearly_trade_profits = df.groupby('year')['cumulative_profit'].sum().to_dict()\n", "        profit_2022 = yearly_trade_profits.get(2022, 0)\n", "        profit_2023 = yearly_trade_profits.get(2023, 0)\n", "        profit_2024 = yearly_trade_profits.get(2024, 0)\n", "        profit_2025 = yearly_trade_profits.get(2025, 0)\n", "\n", "        last_cum_profit = df['cumulative_profit'].iloc[-1] if 'cumulative_profit' in df.columns else 0\n", "        trade_count = sum([1 for k in self.commissions if k > 0])\n", "        total_commission = sum(self.commissions)\n", "        trade_volume = total_commission / self.commission_rate if self.commission_rate > 0 else 0\n", "        self.results.append({\n", "            \"COIN\": coin,\n", "            \"INTERVAL\": interval,\n", "            \"ATR_WINDOW\": atr_window,\n", "            \"ATR_MULT\": atr_mult,\n", "            \"ATR_MULT_ST\": atr_mult_st,\n", "            \"STOP_MULT\": stop_mult,\n", "            \"HIGHLOW_WINDOW\": highlow_window,\n", "            \"PROFIT_2022\": profit_2022,\n", "            \"PROFIT_2023\": profit_2023,\n", "            \"PROFIT_2024\": profit_2024,\n", "            \"PROFIT_2025\": profit_2025,\n", "            \"CUMULATIVE_PROFIT\": last_cum_profit,\n", "            \"TRADE_COUNT\": trade_count,\n", "            \"TOTAL_COMMISSION\": total_commission,\n", "            \"TRADE_VOLUME\": trade_volume,\n", "            \"MAX_PROFIT_LIMIT\": max_profit_limit,  # <-- EKLENDİ\n", "            \"PROFIT_FOLLOW_DISTANCE\": profit_follow_distance,  # <-- EKLENDİ\n", "        })\n", "\n", "    # ...class BacktestMeta: içinde...\n", "\n", "\n", "    # ...existing code...\n", "\n", "    # ...existing code...\n", "\n", "    def _process_final_results(self):\n", "        import pandas as pd\n", "\n", "        summary_rows = []\n", "        monthly_rows = []\n", "        drawdown_zero_rows = []\n", "        consecutive_false_summary = {}\n", "\n", "        for result in self.results:\n", "            excel_file = f\"{result['COIN']}_{result['INTERVAL']}_ATRW{result['ATR_WINDOW']}_ATRm{result['ATR_MULT']}_ATRst{result['ATR_MULT_ST']}_STOPm{result['STOP_MULT']}_HLW{result['HIGHLOW_WINDOW']}_PROFITDIST{result.get('PROFIT_FOLLOW_DISTANCE', '')}.xlsx\"\n", "            try:\n", "                df = pd.read_excel(excel_file)\n", "\n", "                df['year_month'] = df['Open Time'].dt.to_period('M')\n", "                monthly_profit = df.groupby('year_month')['profit_comission_included'].sum()\n", "                yearly_profit = df.groupby(df['Open Time'].dt.year)['profit_comission_included'].sum()\n", "                max_drawdown_last = df['max_cum_profit_drawdown'].iloc[-1]\n", "                max_drawdown_max = df['max_cum_profit_drawdown'].max()\n", "\n", "                drawdown_months = df.groupby('year_month')['max_cum_profit_drawdown'].apply(lambda x: (x == 0).any())\n", "                false_streak = 0\n", "                max_false_streak = 0\n", "                for is_zero in drawdown_months.values:\n", "                    if not is_zero:\n", "                        false_streak += 1\n", "                        if false_streak > max_false_streak:\n", "                            max_false_streak = false_streak\n", "                    else:\n", "                        false_streak = 0\n", "                consecutive_false_summary[result['COIN']] = max_false_streak\n", "\n", "                for ym, is_zero in drawdown_months.items():\n", "                    drawdown_zero_rows.append({\n", "                        \"Coin\": result['COIN'],\n", "                        \"Year-Month\": str(ym),\n", "                        \"Drawdown Zero\": is_zero\n", "                    })\n", "\n", "                for ym, val in monthly_profit.items():\n", "                    monthly_rows.append({\n", "                        \"Coin\": result['COIN'],\n", "                        \"Year-Month\": str(ym),\n", "                        \"Monthly Profit\": val\n", "                    })\n", "                monthly_rows.append({\n", "                    \"Coin\": result['COIN'],\n", "                    \"Year-Month\": \"Ortalama\",\n", "                    \"Monthly Profit\": monthly_profit.mean()\n", "                })\n", "\n", "                summary_rows.append({\n", "                    \"Coin\": result['COIN'],\n", "                    \"ATR Multiplier\": result['ATR_MULT'],\n", "                    \"Supertrend ATR\": result['ATR_MULT_ST'],\n", "                    \"Cumulative Trade Profit (comission included)\": df['profit_comission_included'].sum(),\n", "                    \"Total Commission\": df['commissions'].sum(),\n", "                    \"Monthly Avg Profit\": monthly_profit.mean(),\n", "                    \"Yearly Avg Profit\": yearly_profit.mean(),\n", "                    \"Max Drawdown Last\": max_drawdown_last,\n", "                    \"Max Drawdown Max\": max_drawdown_max,\n", "                    \"Max Consecutive Drawdown Zero False\": max_false_streak,\n", "                    \"ATR_WINDOW\": result['ATR_WINDOW'],\n", "                    \"STOP_MULT\": result['STOP_MULT'],\n", "                    \"HIGHLOW_WINDOW\": result['HIGHLOW_WINDOW'],\n", "                    \"INTERVAL\": result['INTERVAL'],\n", "                    \"MAX_PROFIT_LIMIT\": result.get('MAX_PROFIT_LIMIT', None),  # <-- EKLENDİ\n", "                    \"PROFIT_FOLLOW_DISTANCE\": result.get('PROFIT_FOLLOW_DISTANCE', None),  # <-- EKLENDİ\n", "                })\n", "\n", "            except Exception as e:\n", "                print(f\"Tablo oluşturulamadı: {excel_file} - {e}\")\n", "                continue\n", "\n", "        summary_df = pd.DataFrame(summary_rows)\n", "        monthly_df = pd.DataFrame(monthly_rows)\n", "        drawdown_zero_df = pd.DataFrame(drawdown_zero_rows)\n", "\n", "        # Sıralama: Monthly Avg Profit'e göre büyükten küçüğe\n", "        summary_df = summary_df.sort_values(by=\"Monthly Avg Profit\", ascending=False)\n", "\n", "        # Excel'e yaz ve biçimlendir\n", "        with pd.ExcelWriter(\"tum_sonuclar_sirali_1.xlsx\", engine=\"xlsxwriter\") as writer:\n", "            summary_df.to_excel(writer, sheet_name=\"OzetTablo\", index=False)\n", "            monthly_df.to_excel(writer, sheet_name=\"AylikKarZarar\", index=False)\n", "            drawdown_zero_df.to_excel(writer, sheet_name=\"DrawdownSifirAylar\", index=False)\n", "\n", "            workbook = writer.book\n", "            worksheet = writer.sheets[\"OzetTablo\"]\n", "\n", "            # Tüm tabloya border ekle\n", "            border_format = workbook.add_format({'border': 1})\n", "\n", "            # <PERSON><PERSON><PERSON><PERSON>ı<PERSON>i\n", "            color1 = workbook.add_format({'bg_color': '#F2F2F2', 'border': 1})\n", "            color2 = workbook.add_format({'bg_color': '#DDEBF7', 'border': 1})\n", "\n", "            # Başlıkları bold yap\n", "            header_format = workbook.add_format({'bold': True, 'border': 1, 'bg_color': '#B7DEE8'})\n", "            for col_num, value in enumerate(summary_df.columns.values):\n", "                worksheet.write(0, col_num, value, header_format)\n", "\n", "            # Coin adlarını bold yap\n", "            bold_format = workbook.add_format({'bold': True, 'border': 1})\n", "\n", "            # Satırları yaz ve biçimlendir\n", "            for row_num, row_data in enumerate(summary_df.values, start=1):\n", "                fmt = color1 if row_num % 2 == 0 else color2\n", "                for col_num, cell_data in enumerate(row_data):\n", "                    if col_num == 0:  # Coin adı sütunu\n", "                        worksheet.write(row_num, col_num, cell_data, bold_format)\n", "                    else:\n", "                        worksheet.write(row_num, col_num, cell_data, fmt)\n", "\n", "            # DrawdownSifirAylar tablosunun en altına summary ekle\n", "            worksheet_dd = writer.sheets[\"DrawdownSifirAylar\"]\n", "            last_row = len(drawdown_zero_df) + 2\n", "            worksheet_dd.write(last_row, 0, \"Max Consecutive Drawdown Zero False Summary\")\n", "            for idx, (coin, streak) in enumerate(consecutive_false_summary.items()):\n", "                worksheet_dd.write(last_row + idx + 1, 0, coin, bold_format)\n", "                worksheet_dd.write(last_row + idx + 1, 1, streak, border_format)\n", "\n", "        print(\"<PERSON><PERSON><PERSON> tablo<PERSON> 'tum_sonuclar_sirali_1.xlsx' dosyasına kaydedildi.\")\n", "\n", "        # If no results, print a message\n", "        if not self.results:\n", "            print(\"<PERSON>ç sonuç bulunamadı.\")\n", "\n", "    def try_enter_position(self, row, atr_mult, atr_window, highlow_window, df, i, n_ratio_upper, n_ratio_lower):\n", "        high = row['High']\n", "        low = row['Low']\n", "        atr = row[f'ATR_EMA{atr_window}']\n", "        high_hl = row[f'High_{highlow_window}h']\n", "        low_hl = row[f'Low_{highlow_window}h']\n", "        st_up = row.get('supertrend_upperband', None)\n", "        st_low = row.get('supertrend_lowerband', None)\n", "        turtle_switch, supertrend_switch = self.check_indicator_switches(df, i)\n", "        current_turtle_state = df['turtle_state'].iloc[i]\n", "        current_supertrend_state = df['supertrend_state'].iloc[i]\n", "        self.block_reason = None\n", "        current_atr = atr\n", "\n", "        position_type = None\n", "        entry_price = 1\n", "\n", "\n", "        if turtle_switch and supertrend_switch:\n", "            if current_turtle_state == 'long' and current_supertrend_state == 'long':\n", "                position_type = 'long'\n", "                entry_price = high_hl if high_hl > st_up else st_up\n", "            elif current_turtle_state == 'short' and current_supertrend_state == 'short':\n", "                position_type = 'short'\n", "                entry_price = low_hl if low_hl < st_low else st_low\n", "        elif turtle_switch or supertrend_switch:\n", "            if current_turtle_state == 'long' and current_supertrend_state == 'long':\n", "                position_type = 'long'\n", "                entry_price = high_hl if turtle_switch and not supertrend_switch else st_up\n", "            elif current_turtle_state == 'short' and current_supertrend_state == 'short':\n", "                position_type = 'short'\n", "                entry_price = low_hl if turtle_switch and not supertrend_switch else st_low\n", "        elif (current_turtle_state == 'long' and current_supertrend_state == 'long') or \\\n", "                (current_turtle_state == 'short' and current_supertrend_state == 'short'):\n", "            if current_turtle_state == 'long' and high > high_hl:\n", "                position_type = 'long'\n", "                entry_price = high_hl\n", "            elif current_turtle_state == 'short' and low < low_hl:\n", "                position_type = 'short'\n", "                entry_price = low_hl\n", "            else:\n", "                self.block_reason = 'Fiyat koşulları sağlanmadı' if position_type is None else '<PERSON>yat yeterli degil'\n", "\n", "        atr = 0.004 * entry_price  # ATR'yi N Ratio'ya sabitle\n", "        amount = 0 if (atr is None or np.isnan(atr) or atr == 0) else 20 / (atr_mult * atr)\n", "        calc_n_ratio = atr / entry_price\n", "\n", "        if n_ratio_upper < calc_n_ratio or n_ratio_lower > calc_n_ratio:\n", "            self.block_reason = 'N Ratio'\n", "\n", "        elif position_type and entry_price:\n", "            new_capital, new_balance = self._enter_position(\n", "                position_type, entry_price, amount\n", "            )\n", "            if new_balance != 0:\n", "                self.state = position_type\n", "                self.entry_price = entry_price\n", "                if self.first_entry_price is None:\n", "                    self.first_entry_price = entry_price\n", "\n", "                if self.first_entry_price is None:\n", "                    self.first_entry_price = entry_price\n", "\n", "                if self.state != 'neutral' and self.first_entry_price:\n", "                    current_n_ratio = atr / self.first_entry_price\n", "                else:\n", "                    current_n_ratio = None\n", "                self.n_ratio_list[i] = current_n_ratio\n", "                self.how_many_hours[-1] = 0  # reset counter for instant switch position within the same hour\n", "\n", "                self.entry_atr = atr\n", "                self.pyramid = 1\n", "                self.capital = new_capital\n", "                self.balance = new_balance\n", "                self.pyramid_entries = [entry_price]\n", "                self.pyramid_amounts = [amount]\n", "                self.max_price_during_trade = row['High']\n", "                self.min_price_during_trade = row['Low']\n", "                self.cikis_sebebi = 'Açık Pozisyon'\n", "\n", "                # PROFIT VE MAX_POSSIBLE_PROFIT İŞLEM AÇILIŞINDA YAZILIYOR\n", "                self.trade_profits[i] = 0  # Açılışta profit 0\n", "                self.max_possible_profits[i] = 0  # Açılışta max_possible_profit 0\n", "\n", "                while self.pyramid < self.max_pyramid:\n", "                    new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts, new_pyramid, new_entry_price, pyramid_executed = \\\n", "                        self._execute_pyramid_entry(\n", "                            position_type, high if position_type == 'long' else low, self.entry_price, self.entry_atr,\n", "                            amount\n", "                        )\n", "                    if pyramid_executed:\n", "                        self.capital = new_capital\n", "                        self.balance += new_balance\n", "                        self.pyramid_entries = new_pyramid_entries\n", "                        self.pyramid_amounts = new_pyramid_amounts\n", "                        self.pyramid = new_pyramid\n", "                        self.entry_price = new_entry_price\n", "                        self.cikis_sebebi = f'{position_type} Pyramid'\n", "                    else:\n", "                        break\n", "            else:\n", "                self.block_reason = '<PERSON><PERSON><PERSON>'\n", "\n", "\n", "\n", "# Main execution\n", "if __name__ == \"__main__\":\n", "    backtest = BacktestMeta()\n", "    backtest.run_backtest()"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Could not connect to 127.0.0.1: 58228\n", "Traceback (most recent call last):\n", "  File \"C:\\Program Files\\JetBrains\\PyCharm 2025.1.2\\plugins\\python-ce\\helpers\\pydev\\_pydevd_bundle\\pydevd_comm.py\", line 467, in start_client\n", "    s.connect((host, port))\n", "ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it\n", "Traceback (most recent call last):\n", "  File \"C:\\Program Files\\JetBrains\\PyCharm 2025.1.2\\plugins\\python-ce\\helpers\\jupyter_debug\\pydev_jupyter_utils.py\", line 83, in attach_to_debugger\n", "    debugger.connect(pydev_localhost.get_localhost(), debugger_port)\n", "  File \"C:\\Program Files\\JetBrains\\PyCharm 2025.1.2\\plugins\\python-ce\\helpers\\pydev\\pydevd.py\", line 704, in connect\n", "    s = start_client(host, port)\n", "        ^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"C:\\Program Files\\JetBrains\\PyCharm 2025.1.2\\plugins\\python-ce\\helpers\\pydev\\_pydevd_bundle\\pydevd_comm.py\", line 467, in start_client\n", "    s.connect((host, port))\n", "ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it\n", "Failed to connect to target debugger.\n"]}], "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.4"}}, "nbformat": 4, "nbformat_minor": 5}
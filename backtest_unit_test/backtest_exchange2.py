import os
import pandas as pd
import glob
from datetime import datetime, timedelta
import pytz
from typing import Dict, List, Optional, Union, Tuple
import random

from common.enums.time_interval import TimeInterval
from services.exchange_services.exchange_base import ExchangeBase


class BacktestExchange2(ExchangeBase):
    """
    Enhanced backtesting exchange that properly handles 1-second and 5-minute data
    for more accurate backtesting.
    """
    
    def __init__(self, logger, token: str, 
                 start_time: datetime, end_time: datetime,
                 data_dir: str = None, interval: TimeInterval = TimeInterval.FIFTEEN_MIN,
                 leverage: float = 1.0):
        """
        Initialize the backtesting exchange.
        
        Args:
            logger: Logger instance
            token: Trading pair symbol (e.g., 'AVAXUSDT')
            start_time: Start time for the backtest
            end_time: End time for the backtest
            data_dir: Directory containing candle data files
            leverage: Leverage to use for trading
        """
        self.logger = logger
        self.token = token
        self.start_time = start_time
        self.end_time = end_time
        self.current_time = start_time + timedelta(minutes=5)
        self.leverage = leverage
        
        # Set default data directory if not provided
        if data_dir is None:
            self.data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                         'data', 'candles')
        else:
            self.data_dir = data_dir
            
        # Create output directory for results
        self.output_dir = os.path.join(os.path.dirname(self.data_dir), 'backtest_results')
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Load data
        self.logger.info(f"Loading data for {token} from {start_time} to {end_time}")

        self.five_min_data = self._load_binance_candle_data(interval)
        self.one_second_data = self._load_one_second_data()

        # Current price tracking
        self.current_price = 0.0
        self.current_second_index = 0
        
        # Order tracking
        self.orders = []
        self.positions = []
        self.current_position = {'symbol': token, 'positionAmt': 0.0, 'entryPrice': 0.0}
        
        # For API compatibility
        self.client = self  # Make self.client point to self for API compatibility
        
        # Quantity precision (for order size rounding)
        self.quantity_precision = 3
        
        # Log data summary
        if not self.one_second_data.empty:
            self.logger.info(f"Loaded {len(self.one_second_data)} 1-second candles from "
                            f"{self.one_second_data['datetime'].iloc[0]} to "
                            f"{self.one_second_data['datetime'].iloc[-1]}")
        
        if not self.five_min_data.empty:
            self.logger.info(f"Loaded {len(self.five_min_data)} 5-minute candles from "
                            f"{self.five_min_data['datetime'].iloc[0]} to "
                            f"{self.five_min_data['datetime'].iloc[-1]}")
    
    def _load_one_second_data(self) -> pd.DataFrame:
        """
        Simulate 1-second data from 5-minute candles
        
        This method generates synthetic 1-second data from 5-minute candles,
        ensuring that the simulated data touches each OHLC value at some point
        within the 5-minute period.
        
        Returns:
            DataFrame containing simulated 1-second data
        """
        return self._simulate_one_second_data_from_five_min(self.five_min_data)
        
    def _simulate_one_second_data_from_five_min(self, five_min_data: pd.DataFrame) -> pd.DataFrame:
        """
        Simulate 1-second data from 5-minute candles with realistic price movements
        
        This method generates synthetic 1-second data from 5-minute candles,
        ensuring that the simulated data touches each OHLC value at some point
        within the 5-minute period, and creates a realistic price path.
        
        Args:
            five_min_data: DataFrame containing 5-minute candle data
            
        Returns:
            DataFrame containing simulated 1-second data
        """
        if five_min_data.empty:
            self.logger.warning("No 5-minute data available to simulate 1-second data")
            return pd.DataFrame()
        
        self.logger.info(f"Simulating 1-second data from {len(five_min_data)} 5-minute candles")
        
        # Create an empty list to store the simulated 1-second data
        simulated_data = []
        
        # Process each 5-minute candle
        for idx, candle in five_min_data.iterrows():
            # Extract OHLC values from the 5-minute candle
            open_price = float(candle['open'])
            high_price = float(candle['high'])
            low_price = float(candle['low'])
            close_price = float(candle['close'])
            
            # Get the candle's start time
            if 'datetime_obj' in candle:
                candle_start_time = candle['datetime_obj']
            else:
                candle_start_time = pd.to_datetime(candle['datetime'], utc=True)
            
            # Calculate the next candle's start time (5 minutes later)
            next_candle_start_time = candle_start_time + timedelta(minutes=5)
            
            # Generate 300 seconds (5 minutes) of data
            # Note: We'll actually generate 299 seconds since the last second
            # will be the open of the next candle
            num_seconds = 299
            
            # Determine key points where the price will touch OHLC values
            # We'll divide the 5-minute period into sections and randomly select
            # points where the price will touch the high and low values
            
            # Randomly select seconds where the price will touch high and low values
            # Ensure high comes before low if the close is lower than open (downtrend)
            # and low comes before high if the close is higher than open (uptrend)
            if close_price < open_price:  # Downtrend
                high_second = random.randint(10, num_seconds // 3)
                # Make sure we have at least 10 seconds between high and low periods
                low_second = random.randint(high_second + 12, num_seconds - 10)
            else:  # Uptrend or sideways
                low_second = random.randint(10, num_seconds // 3)
                # Make sure we have at least 10 seconds between low and high periods
                high_second = random.randint(low_second + 12, num_seconds - 10)
            
            # Create a price path that ensures we touch all OHLC values
            # Start with the open price
            current_price = open_price
            
            for second in range(num_seconds + 1):
                current_time = candle_start_time + timedelta(seconds=second)
                
                # Determine the target price for this second
                if second == 0:
                    # First second is always the open price
                    target_price = open_price
                elif second == high_second or second == high_second + 1:
                    # Touch the high price for two consecutive seconds
                    target_price = high_price
                elif second == low_second or second == low_second + 1:
                    # Touch the low price for two consecutive seconds
                    target_price = low_price
                elif second == num_seconds:
                    # Last second is always the close price
                    target_price = close_price
                else:
                    # For other seconds, move towards the next key point
                    if second < high_second and second < low_second:
                        # Moving towards whichever comes first
                        if high_second < low_second:
                            # Moving towards high
                            ratio = second / high_second
                            target_price = open_price + ratio * (high_price - open_price)
                        else:
                            # Moving towards low
                            ratio = second / low_second
                            target_price = open_price + ratio * (low_price - open_price)
                    elif second < high_second:
                        # Already passed low, moving towards high
                        if second > low_second + 1:  # After the 2-second low period
                            ratio = (second - (low_second + 1)) / (high_second - (low_second + 1))
                            target_price = low_price + ratio * (high_price - low_price)
                        else:
                            target_price = low_price
                    elif second < low_second:
                        # Already passed high, moving towards low
                        if second > high_second + 1:  # After the 2-second high period
                            ratio = (second - (high_second + 1)) / (low_second - (high_second + 1))
                            target_price = high_price + ratio * (low_price - high_price)
                        else:
                            target_price = high_price
                    else:
                        # Passed both high and low, moving towards close
                        last_key_second = max(high_second, low_second) + 1  # Account for 2-second periods
                        ratio = (second - last_key_second) / (num_seconds - last_key_second)
                        last_key_price = high_price if high_second > low_second else low_price
                        target_price = last_key_price + ratio * (close_price - last_key_price)
                
                # For the designated high and low seconds, make sure we exactly hit those values
                if second == high_second or second == high_second + 1:
                    current_price = high_price
                elif second == low_second or second == low_second + 1:
                    current_price = low_price
                else:
                    # Update the current price with some inertia (don't jump directly to target)
                    current_price = current_price * 0.7 + target_price * 0.3
                if current_time > datetime(2025, 5, 21, 11, 5, 0, tzinfo=pytz.UTC) and \
                    current_time < datetime(2025, 5, 21, 11, 10, 0, tzinfo=pytz.UTC):
                    self.logger.info(f"Current price: {current_time} {current_price}")
                simulated_data.append({
                    'time': int(current_time.timestamp() * 1000),
                    'datetime': current_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'datetime_obj': current_time,
                    'open': current_price,
                    'high': current_price,
                    'low': current_price,
                    'close': current_price,
                    'volume': float(candle['volume']) / num_seconds  # Distribute volume evenly
                })
        
        # Convert to DataFrame
        df = pd.DataFrame(simulated_data)
        
        # Sort by time to ensure chronological order
        df = df.sort_values('time')
        
        self.logger.info(f"Successfully simulated {len(df)} 1-second data points")
        return df
    
    def _load_binance_candle_data(self, interval: TimeInterval) -> pd.DataFrame:
        """Load 5-minute candle data for the specified time range"""
        # Format timestamps for filename
        buffer_hours = 5 * 200 / 60
        start_dt = self.start_time - timedelta(hours=buffer_hours)
        end_dt = self.end_time
        start_str = start_dt.strftime('%Y%m%d%H%M')
        end_str = end_dt.strftime('%Y%m%d%H%M')
        
        # Look for a specific file matching the start and end dates
        file_path = os.path.join(self.data_dir, f"{self.token}-{interval.value}-{start_str}-{end_str}.csv")

        try:
            # Load data from CSV with header
            df = pd.read_csv(file_path)
            
            # Convert timestamps to datetime objects if not already
            if 'datetime_obj' not in df.columns:
                df['datetime_obj'] = pd.to_datetime(df['datetime'], utc=True)
            
            # Add to list
            self.logger.info(f"Loaded data from {file_path}")
        except Exception as e:
            self.logger.error(f"Error loading {file_path}: {e}")
            self.logger.warning(f"Collecting data from Binance")
            try:
                df = self.fetch_binance_candle_data(
                    symbol=self.token,
                    start_time=start_dt.replace(tzinfo=pytz.UTC),
                    end_time=end_dt.replace(tzinfo=pytz.UTC),
                    interval=interval,
                    save_to_file=True
                )
            except Exception as e:
                self.logger.error(f"Error collecting 5-minute data from Binance: {e}")
                return pd.DataFrame()

        df = df.sort_values('time').reset_index(drop=True)
        
        return df

    def get_current_futures_price(self, symbol) -> float:
        if self.one_second_data.empty:
            raise ValueError("No 1-second data loaded")

        # Find the closest timestamp to current time
        current_time = self.current_time
        current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')

        current_second_data = self.one_second_data[self.one_second_data['datetime'] == current_time_str]
        data_row = current_second_data.iloc[0]

        current_data = data_row
        if current_data is None:
            raise ValueError("No 1-second data available")

        self.current_price = float(current_data['close'])

        self.logger.debug(f"Current futures price for {symbol} at {self.current_time.strftime('%Y-%m-%d %H:%M:%S')}: {self.current_price}")
        return self.current_price
    
    def advance_time(self, seconds: int = 1):
        """Advance the current time by the specified number of seconds"""
        self.current_time += timedelta(seconds=seconds)
        self.current_second_index += seconds


    def get_and_check_candle(self, symbol: str, interval: TimeInterval) -> pd.DataFrame:
        """Get candle data for the specified symbol and interval"""
        # Filter candles up to current time
        min_dict = {
            "5m": 5,
            "15m": 15,
            "30m": 30,
            "1h": 60
        }
        current_time = self.current_time -timedelta(minutes=min_dict[interval.value])
        current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')
        filtered_df = self.five_min_data[self.five_min_data['datetime'] < current_time_str].copy()
        
        # Log the candles being used
        self.logger.debug(f"Using {len(filtered_df)} 5-minute candles up to {filtered_df['datetime'].iloc[-1]}")
        
        return filtered_df
    
    def execute_order(self, symbol: str, side: str, amount: float, leverage: float, 
                      reduce_only: bool = False) -> Dict:
        """
        Execute a simulated order in the backtest
        
        Args:
            symbol: Trading pair symbol
            side: 'BUY' or 'SELL'
            amount: Amount to trade
            leverage: Leverage to use
            reduce_only: Whether this order should only reduce position
            
        Returns:
            Dict containing order details
        """
        # Round amount to precision
        amount = round(float(amount), self.quantity_precision)
        
        # Create order object
        order = {
            'symbol': symbol,
            'side': side,
            'amount': amount,
            'price': self.current_price,
            'time': self.current_time.strftime('%Y-%m-%d %H:%M:%S'),
            'reduce_only': reduce_only,
            'leverage': leverage
        }
        
        # Update position
        current_position_amt = float(self.current_position['positionAmt'])
        
        if side == 'BUY':
            if reduce_only and current_position_amt < 0:
                # Reduce short position
                new_position_amt = min(0, current_position_amt + amount)
                executed_amount = current_position_amt - new_position_amt
            else:
                # Increase long position
                new_position_amt = current_position_amt + amount
                executed_amount = amount
        else:  # SELL
            if reduce_only and current_position_amt > 0:
                # Reduce long position
                new_position_amt = max(0, current_position_amt - amount)
                executed_amount = current_position_amt - new_position_amt
            else:
                # Increase short position
                new_position_amt = current_position_amt - amount
                executed_amount = amount
        
        # Update position
        self.current_position = {
            'symbol': symbol,
            'positionAmt': new_position_amt,
            'entryPrice': self.current_price if new_position_amt != 0 else 0.0
        }
        
        # Add executed amount to order
        order['executed_amount'] = executed_amount
        
        # Add to orders list
        self.orders.append(order)
        
        # Log the order
        self.logger.info(
            f"Order executed: {side} {executed_amount} {symbol} @ {self.current_price} "
            f"(Position: {new_position_amt})"
        )
        
        return order
    
    def futures_position_information(self, symbol: str = None) -> List[Dict]:
        """Get current position information"""
        return [self.current_position]
    
    def get_binance_spot_quantity_precisions(self) -> Dict:
        """Get quantity precision for all symbols"""
        return {self.token: self.quantity_precision}
    
    def save_backtest_results(self, output_dir: str = None, atr_filter_ratio: float = None, pnl: float = None, pnl_percentage: float = None) -> Tuple[str, str]:
        """
        Save backtest results to CSV files
        
        Args:
            output_dir: Directory to save results (defaults to self.output_dir)
            atr_filter_ratio: ATR filter ratio used in the backtest
            pnl: Profit and loss value in USD
            pnl_percentage: Profit and loss percentage
            
        Returns:
            Tuple of (orders_file_path, positions_file_path)
        """
        if output_dir is None:
            output_dir = self.output_dir
            

        # Format timestamps for filenames
        start_str = self.start_time.strftime('%Y%m%d')
        end_str = self.end_time.strftime('%Y%m%d')
        
        # Save orders
        orders_df = pd.DataFrame(self.orders)
        if not orders_df.empty:
            orders_file = output_dir + ".csv"
            
            # Add metadata as a comment at the top of the CSV file
            with open(orders_file, 'w') as f:
                f.write(f"# Backtest Results for {self.token}\n")
                f.write(f"# Time Period: {self.start_time} to {self.end_time}\n")
                if atr_filter_ratio is not None:
                    f.write(f"# ATR Filter Ratio: {atr_filter_ratio}\n")
                if pnl is not None:
                    f.write(f"# PnL: ${pnl:.2f}\n")
                if pnl_percentage is not None:
                    f.write(f"# PnL Percentage: {pnl_percentage:.2f}%\n")
                f.write("\n")
            
            # Append the orders data
            orders_df.to_csv(orders_file, index=False, mode='a')
            self.logger.info(f"Saved {len(orders_df)} orders to {orders_file}")
        else:
            orders_file = None
            self.logger.info("No orders to save")
        
        # Save positions
        positions_df = pd.DataFrame(self.positions)
        if not positions_df.empty:
            positions_file = os.path.join(output_dir, f"{self.token}_positions_{start_str}_{end_str}.csv")
            positions_df.to_csv(positions_file, index=False)
            self.logger.info(f"Saved {len(positions_df)} positions to {positions_file}")
        else:
            positions_file = None
            self.logger.info("No positions to save")
            
        return orders_file, positions_file
        
    def fetch_binance_candle_data(self, symbol: str, start_time: datetime, end_time: datetime, interval: TimeInterval, save_to_file: bool = True) -> pd.DataFrame:
        """
        Collect 5-minute candle data from Binance for the specified symbol and time range
        
        Args:
            symbol: Trading pair symbol (e.g., 'AVAXUSDT')
            start_time: Start time for data collection
            end_time: End time for data collection
            save_to_file: Whether to save the collected data to a file
            
        Returns:
            DataFrame containing the collected 5-minute candle data
        """
        try:
            from binance.client import Client
            from binance.exceptions import BinanceAPIException, BinanceRequestException
        except ImportError:
            self.logger.error("Binance package not installed. Install with: pip install python-binance")
            return pd.DataFrame()
            
        self.logger.info(f"Collecting 5-minute data from Binance for {symbol} from {start_time} to {end_time}")
        
        # Initialize Binance client
        try:
            # Try to connect without API keys first (for public data)
            client = Client()
            
            # Convert time to milliseconds timestamp
            start_ts = int(start_time.timestamp() * 1000)
            end_ts = int(end_time.timestamp() * 1000)
            
            # Fetch klines data from Binance
            klines = client.futures_klines(
                symbol=symbol,
                interval=interval.value,
                startTime=start_ts,
                endTime=end_ts,
                limit=1000  # Maximum allowed by Binance
            )
            
            # Process the data
            data = []
            for candle in klines:
                data.append({
                    "time": candle[0],
                    "datetime": datetime.utcfromtimestamp(candle[0] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                    "open": round(float(candle[1]), 4),
                    "high": round(float(candle[2]), 4),
                    "low": round(float(candle[3]), 4),
                    "close": round(float(candle[4]), 4),
                    "volume": round(float(candle[5]), 4),
                    "count": int(candle[8])
                })
            
            # Create DataFrame
            df = pd.DataFrame(data)
            
            if df.empty:
                self.logger.warning(f"No 5-minute data found for {symbol} in the specified time range")
                return df
                
            self.logger.info(f"Collected {len(df)} 5-minute candles from Binance")
            
            # Save to file if requested
            if save_to_file:
                # Create data directory if it doesn't exist
                os.makedirs(self.data_dir, exist_ok=True)
                
                # Format timestamps for filename
                start_str = start_time.strftime('%Y%m%d')
                end_str = end_time.strftime('%Y%m%d')
                
                # Save to CSV
                file_path = os.path.join(self.data_dir, f"{symbol}-5m-{start_str}-{end_str}.csv")
                df.to_csv(file_path, index=False)
                self.logger.info(f"Saved 5-minute data to {file_path}")
            
            return df
            
        except (BinanceAPIException, BinanceRequestException) as e:
            self.logger.error(f"Error fetching 5-minute data from Binance: {e}")
            return pd.DataFrame()
        except Exception as e:
            self.logger.error(f"Unexpected error collecting 5-minute data: {e}")
            return pd.DataFrame()

    def collect_binance_5m_data_in_batches(self, symbol: str, start_time: datetime, end_time: datetime, save_to_file: bool = True) -> pd.DataFrame:
        """
        Collect 5-minute candle data from Binance for the specified symbol and time range in batches of 1000 candles.
        
        This method handles the Binance API limit of 1000 records per request by making multiple requests
        and combining the results.
        
        Args:
            symbol: Trading pair symbol (e.g., 'AVAXUSDT')
            start_time: Start time for data collection
            end_time: End time for data collection
            save_to_file: Whether to save the collected data to a file
            
        Returns:
            DataFrame containing the collected 5-minute candle data
        """
        try:
            from binance.client import Client
            from binance.exceptions import BinanceAPIException, BinanceRequestException
            
            # Initialize Binance client
            client = Client()
            
            # Initialize variables
            all_candles = []
            current_start_time = start_time.replace(tzinfo=pytz.UTC)
            batch_count = 0
            
            self.logger.info(f"Starting batch collection of 5-minute candles for {symbol} from {start_time} to {end_time}")
            
            # Loop until we've collected all data or reached the end time
            while current_start_time < end_time:
                # Convert time to milliseconds timestamp
                start_ts = int(current_start_time.timestamp() * 1000)
                end_ts = int(end_time.timestamp() * 1000)
                
                # Fetch klines data from Binance (1000 is the maximum allowed)
                klines = client.futures_klines(
                    symbol=symbol,
                    interval=Client.KLINE_INTERVAL_5MINUTE,
                    startTime=start_ts,
                    endTime=end_ts,
                    limit=1000
                )
                
                # Break if no more data
                if not klines:
                    break
                    
                batch_count += 1
                self.logger.info(f"Collected batch {batch_count} with {len(klines)} candles")
                
                # Process the data
                for candle in klines:
                    candle_time = datetime.utcfromtimestamp(candle[0] / 1000)
                    all_candles.append({
                        "time": candle[0],
                        "datetime": candle_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "open": round(float(candle[1]), 4),
                        "high": round(float(candle[2]), 4),
                        "low": round(float(candle[3]), 4),
                        "close": round(float(candle[4]), 4),
                        "volume": round(float(candle[5]), 4),
                        "count": int(candle[8])
                    })
                
                # Update start time for next batch (add 1 millisecond to last candle time to avoid duplicates)
                if klines:
                    last_candle_time = datetime.utcfromtimestamp(klines[-1][0] / 1000)
                    current_start_time = last_candle_time.replace(tzinfo=pytz.UTC) + timedelta(milliseconds=1)
                
                # Add a small delay to avoid hitting rate limits
                import time
                time.sleep(0.5)
            
            # Create DataFrame from all collected candles
            df = pd.DataFrame(all_candles)
            
            if df.empty:
                self.logger.warning(f"No 5-minute data found for {symbol} in the specified time range")
                return df
                
            self.logger.info(f"Collected a total of {len(df)} 5-minute candles across {batch_count} batches")
            
            # Save to file if requested
            if save_to_file:
                # Create data directory if it doesn't exist
                os.makedirs(self.data_dir, exist_ok=True)
                
                # Format timestamps for filename
                start_str = start_time.strftime('%Y%m%d')
                end_str = end_time.strftime('%Y%m%d')
                
                # Save to CSV
                file_path = os.path.join(self.data_dir, f"{symbol}-5m-{start_str}-{end_str}.csv")
                df.to_csv(file_path, index=False)
                self.logger.info(f"Saved 5-minute data to {file_path}")
            
            return df
            
        except (BinanceAPIException, BinanceRequestException) as e:
            self.logger.error(f"Error fetching 5-minute data from Binance: {e}")
            return pd.DataFrame()
        except Exception as e:
            self.logger.error(f"Unexpected error collecting 5-minute data in batches: {e}")
            return pd.DataFrame()

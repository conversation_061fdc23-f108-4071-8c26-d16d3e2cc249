

def get_interval_seconds(interval: str) -> int:
    """Convert interval string to seconds"""
    if interval.endswith('m'):
        return int(interval[:-1]) * 60
    elif interval.endswith('h'):
        return int(interval[:-1]) * 3600
    elif interval.endswith('d'):
        return int(interval[:-1]) * 86400
    else:
        return 300  # Default to 5 minutes


def calculate_pnl(orders: List[Dict], token: str, logger: logging.Logger) -> Tuple[float, float]:
    """Calculate profit and loss from orders"""
    buy_volume = 0.0
    sell_volume = 0.0
    buy_value = 0.0
    sell_value = 0.0

    for order in orders:
        if order['side'] == 'BUY':
            buy_volume += order['executed_amount']
            buy_value += order['executed_amount'] * order['price']
        else:  # SELL
            sell_volume += order['executed_amount']
            sell_value += order['executed_amount'] * order['price']

    # Calculate P&L
    pnl = 0.0
    pnl_percentage = 0.0

    if buy_volume > 0 and sell_volume > 0:
        avg_buy_price = buy_value / buy_volume
        avg_sell_price = sell_value / sell_volume

        pnl = (avg_sell_price - avg_buy_price) * min(buy_volume, sell_volume)
        pnl_percentage = (pnl / buy_value) * 100 if buy_value > 0 else 0

        logger.info(f"Backtest P&L Summary:")
        logger.info(f"  Total buy volume: {buy_volume} {token}")
        logger.info(f"  Total sell volume: {sell_volume} {token}")
        logger.info(f"  Average buy price: ${avg_buy_price:.6f}")
        logger.info(f"  Average sell price: ${avg_sell_price:.6f}")
        logger.info(f"  Estimated P&L: ${pnl:.2f} ({pnl_percentage:.2f}%)")
    else:
        logger.info("Insufficient trades to calculate P&L")

    return pnl, pnl_percentage


def setup_logger(log_level=logging.INFO, log_file=None):
    """Set up and configure logger"""
    # Create logger
    logger = logging.getLogger('SuperArchelon')
    logger.setLevel(log_level)

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # Create file handler if log file specified
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='SuperArchelon Backtesting Tool')

    parser.add_argument('--token', type=str, default='AVAXUSDT',
                        help='Trading pair to backtest (default: AVAXUSDT)')

    parser.add_argument('--interval', type=str, default='1h',
                        help='Candle interval for trading (default: 5m)')

    parser.add_argument('--account-size', type=float, default=100000.0,
                        help='Account size in USD (default: 100000.0)')

    parser.add_argument('--risk-percentage', type=float, default=1.0,
                        help='Risk percentage per trade (default: 1.0)')

    parser.add_argument('--leverage', type=float, default=1.0,
                        help='Leverage to use (default: 1.0)')

    parser.add_argument('--data-dir', type=str, default=None,
                        help='Directory containing candle data (default: ./data/candles)')

    parser.add_argument('--output-dir', type=str, default=None,
                        help='Directory to save backtest results (default: ./data/backtest_results)')

    parser.add_argument('--log-file', type=str, default=None,
                        help='Log file path (default: None, logs to console only)')

    parser.add_argument('--log-level', type=str, default='INFO',
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='Logging level (default: INFO)')

    parser.add_argument('--instant-loop-interval', type=int, default=2,
                        help='Interval in seconds between instant loop iterations (default: 1)')

    parser.add_argument('--atr_filter_lower', type=str, default='0.009',
                        help='')

    parser.add_argument('--atr_filter_upper', type=str, default='0.005',
                        help='Comma-separated list of atr_filter_lower values to test')

    parser.add_argument('--parallel', action='store_true',
                        help='Use parallel processing for testing multiple atr_filter_ratios')

    parser.add_argument('--processes', type=int, default=None,
                        help='Number of parallel processes to use (default: number of CPU cores)')

    parser.add_argument('--atr_multiplier', type=str, default='2.0')
    parser.add_argument('--atr_stop_multiplier', type=str, default='2.0')
    parser.add_argument('--supertrend_atr', type=str, default='3.5')
    parser.add_argument('--position_preference', type=str, default='both')

    return parser.parse_args()

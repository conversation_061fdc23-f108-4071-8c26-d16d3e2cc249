#!/usr/bin/env python3
import os
import logging
import argparse
from datetime import datetime, timedelta
import pytz
import time
from typing import Dict, List, Tuple
import multiprocessing
from multiprocessing import Pool
import json

from common.enums.time_interval import TimeInterval
from logic.trader import SuperArchelon
from logic.data_containers.trader_parameters import TraderParameters
from backtest_unit_test.backtest_exchange2 import BacktestExchange2
from utils import setup_logger, parse_arguments, get_interval_seconds, calculate_pnl


def run_backtest(args):
    """Run the backtest with the specified parameters"""
    current_atr_filter_upper = float(args.atr_filter_upper)
    current_atr_filter_lower = float(args.atr_filter_lower)
    # Set up logger
    log_level = getattr(logging, args.log_level)
    
    log_file = args.log_file
    if args.parallel and log_file:
        base, ext = os.path.splitext(log_file)
        log_file = f"{base}_atr_{current_atr_filter_upper}{ext}"
    
    logger = setup_logger(log_level, log_file)
    
    # Hard-coded dates for the backtest
    start_date = datetime.strptime('2025-07-22 18:00:00', '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC)
    end_date = datetime.strptime('2025-07-23 07:00:00', '%Y-%m-%d %H:%M:%S').replace(tzinfo=pytz.UTC)
    
    # Set up data directory
    if args.data_dir is None:
        args.data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'candles')
    
    # Set up output directory
    if args.output_dir is None:
        # Format start and end dates for directory name
        start_str = start_date.strftime('%Y%m%d%H%M')
        end_str = end_date.strftime('%Y%m%d%H%M')
        args.output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                      'data',
                                      'backtest_results',
                                      f"{args.token}-{start_str}-{end_str}")
    
    output_dir = args.output_dir
    if args.parallel:
        output_dir = os.path.join(output_dir, f"atr_{current_atr_filter_upper}")
    
    # Log backtest parameters
    logger.info(f"Starting backtest with parameters:")
    logger.info(f"  Token: {args.token}")
    logger.info(f"  Interval: {args.interval}")
    logger.info(f"  Start date: {start_date}")
    logger.info(f"  End date: {end_date}")
    logger.info(f"  Account size: ${args.account_size}")
    logger.info(f"  Risk percentage: {args.risk_percentage}%")
    logger.info(f"  Leverage: {args.leverage}x")
    logger.info(f"  ATR Filter Ratio: {current_atr_filter_upper}")
    logger.info(f"  Data directory: {args.data_dir}")
    logger.info(f"  Output directory: {output_dir}")

    # Set up exchange
    exchange = BacktestExchange2(
        logger=logger,
        token=args.token,
        start_time=start_date,
        end_time=end_date,
        data_dir=args.data_dir,
        interval=TimeInterval(args.interval),
        leverage=args.leverage
    )
    
    # Set up trader parameters
    trader_params = TraderParameters(
        token=args.token,
        interval=TimeInterval(args.interval),
        amount_from_config=args.account_size * (args.risk_percentage / 100),
        leverage=int(args.leverage),
        atr_filter_upper=current_atr_filter_upper,
        atr_filter_lower=current_atr_filter_lower,
        position_preference='both'
    )
    
    # Set up trader
    trader = SuperArchelon(
        token=args.token,
        trade_params=trader_params,
        logger=logger,
        exchange=exchange
    )
    
    # Run backtest
    current_time = start_date
    last_interval_time = None
    last_instant_time = None
    interval_seconds = get_interval_seconds(args.interval)
    
    try:
        while current_time <= end_date:
            # Update logger with current timestamp for consistent logging
            logger.extra = {'timestamp': current_time}
            
            # Check if we need to run interval loop
            if last_interval_time is None or (current_time - last_interval_time).total_seconds() >= interval_seconds:
                logger.debug(f"Running interval loop at {current_time}")
                trader.interval_loop()
                last_interval_time = current_time

            # Run instant loop every 2 seconds
            if last_instant_time is None or (current_time - last_instant_time).total_seconds() >= 2:
                logger.debug(f"Running instant loop at {current_time}")
                trader.instant_loop()
                last_instant_time = current_time
            
            # Advance time
            current_time += timedelta(seconds=args.instant_loop_interval)
            exchange.advance_time(args.instant_loop_interval)
            
            # Add a small delay to prevent CPU overload during testing
            time.sleep(0.001)
    
    except KeyboardInterrupt:
        logger.info("Backtest interrupted by user")
    except Exception as e:
        logger.error(f"Error during backtest: {e}", exc_info=True)
    finally:
        # Save backtest results
        logger.info("Saving backtest results...")
        os.makedirs(output_dir, exist_ok=True)
        
        # Calculate P&L if we have orders
        pnl = 0.0
        pnl_percentage = 0.0
        if exchange.orders:
            pnl, pnl_percentage = calculate_pnl(exchange.orders, args.token, logger)
        
        # Save results with atr_filter_ratio and pnl information
        orders_file, positions_file = exchange.save_backtest_results(
            output_dir=output_dir,
            atr_filter_ratio=current_atr_filter_upper,
            pnl=pnl,
            pnl_percentage=pnl_percentage
        )
        
        # Log summary
        logger.info("Backtest completed")
        logger.info(f"  Orders: {len(exchange.orders)}")
        logger.info(f"  Final position: {exchange.current_position['positionAmt']} {args.token}")
        
        # Calculate P&L if we have orders
        pnl = 0.0
        pnl_percentage = 0.0
        if exchange.orders:
            pnl, pnl_percentage = calculate_pnl(exchange.orders, args.token, logger)
        
        # Save summary results to a JSON file for easy comparison
        summary = {
            "atr_filter_ratio": current_atr_filter_upper,
            "orders_count": len(exchange.orders),
            "pnl": pnl,
            "pnl_percentage": pnl_percentage,
            "final_position": exchange.current_position['positionAmt']
        }
        
        summary_file = os.path.join(output_dir, "summary.json")
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        return summary


if __name__ == '__main__':
    # Parse arguments
    args = parse_arguments()
   

    run_backtest(args)

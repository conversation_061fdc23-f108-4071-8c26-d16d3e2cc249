{"cells": [{"cell_type": "code", "execution_count": null, "id": "6e82d4a0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Başlıyor: AVAXUSDT 15m ATRwin:20 ATRmult:2 STOPmult:2 HLwin:20\n", "\n", "DataFrame Structure:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 55186 entries, 0 to 55185\n", "Data columns (total 33 columns):\n", " #   Column                     Non-Null Count  Dtype         \n", "---  ------                     --------------  -----         \n", " 0   Open Time                  55186 non-null  datetime64[ns]\n", " 1   Open                       55186 non-null  float64       \n", " 2   High                       55186 non-null  float64       \n", " 3   Low                        55186 non-null  float64       \n", " 4   Close                      55186 non-null  float64       \n", " 5   TR                         55186 non-null  float64       \n", " 6   ATR_EMA20                  55185 non-null  float64       \n", " 7   Low_20h                    55166 non-null  float64       \n", " 8   High_20h                   55166 non-null  float64       \n", " 9   Low_10h                    55176 non-null  float64       \n", " 10  High_10h                   55176 non-null  float64       \n", " 11  supertrend                 55186 non-null  float64       \n", " 12  supertrend_state           55186 non-null  object        \n", " 13  supertrend_upperband       55186 non-null  float64       \n", " 14  supertrend_lowerband       55186 non-null  float64       \n", " 15  donchian_high              55166 non-null  float64       \n", " 16  donchian_low               55166 non-null  float64       \n", " 17  turtle_state               55186 non-null  object        \n", " 18  State                      55186 non-null  object        \n", " 19  amount                     55186 non-null  float64       \n", " 20  entry_price                39461 non-null  float64       \n", " 21  pyramid                    55186 non-null  int64         \n", " 22  exit_reason                12290 non-null  object        \n", " 23  exit_price                 1799 non-null   float64       \n", " 24  commissions                55186 non-null  float64       \n", " 25  profit                     55186 non-null  float64       \n", " 26  entry_commissions          55186 non-null  float64       \n", " 27  exit_commissions           55186 non-null  float64       \n", " 28  profit_comission_included  55186 non-null  float64       \n", " 29  cumulative_profit          55186 non-null  float64       \n", " 30  max_possible_profit        55186 non-null  float64       \n", " 31  n_ratio                    1800 non-null   float64       \n", " 32  max_cum_profit_drawdown    55186 non-null  float64       \n", "dtypes: datetime64[ns](1), float64(27), int64(1), object(4)\n", "memory usage: 13.9+ MB\n", "None\n", "\n", "First 5 rows of DataFrame:\n", "            Open Time    Open    High     Low   Close     TR  ATR_EMA20  \\\n", "0 2023-12-31 23:00:00  38.254  38.596  38.154  38.442  0.442        NaN   \n", "1 2023-12-31 23:15:00  38.443  38.513  38.213  38.408  0.300   0.442000   \n", "2 2023-12-31 23:30:00  38.407  38.661  38.403  38.643  0.258   0.428476   \n", "3 2023-12-31 23:45:00  38.642  38.706  38.471  38.589  0.235   0.412240   \n", "4 2024-01-01 00:00:00  38.590  38.942  38.519  38.931  0.423   0.395360   \n", "\n", "   Low_20h  High_20h  Low_10h  ...  exit_price  commissions profit  \\\n", "0      NaN       NaN      NaN  ...         NaN          0.0    0.0   \n", "1      NaN       NaN      NaN  ...         NaN          0.0    0.0   \n", "2      NaN       NaN      NaN  ...         NaN          0.0    0.0   \n", "3      NaN       NaN      NaN  ...         NaN          0.0    0.0   \n", "4      NaN       NaN      NaN  ...         NaN          0.0    0.0   \n", "\n", "   entry_commissions  exit_commissions  profit_comission_included  \\\n", "0                0.0               0.0                        0.0   \n", "1                0.0               0.0                        0.0   \n", "2                0.0               0.0                        0.0   \n", "3                0.0               0.0                        0.0   \n", "4                0.0               0.0                        0.0   \n", "\n", "   cumulative_profit max_possible_profit n_ratio  max_cum_profit_drawdown  \n", "0                0.0                 0.0     NaN                      0.0  \n", "1                0.0                 0.0     NaN                      0.0  \n", "2                0.0                 0.0     NaN                      0.0  \n", "3                0.0                 0.0     NaN                      0.0  \n", "4                0.0                 0.0     NaN                      0.0  \n", "\n", "[5 rows x 33 columns]\n", "\n", "Turtle State Analysis:\n", "                Open Time   Close  donchian_high  donchian_low turtle_state  \\\n", "55176 2025-07-28 17:00:00  25.889         27.415        25.859        short   \n", "55177 2025-07-28 17:15:00  26.009         27.415        25.850        short   \n", "55178 2025-07-28 17:30:00  25.819         27.382        25.751        short   \n", "55179 2025-07-28 17:45:00  25.799         27.382        25.751        short   \n", "55180 2025-07-28 18:00:00  25.739         27.382        25.729        short   \n", "55181 2025-07-28 18:15:00  25.539         27.304        25.583        short   \n", "55182 2025-07-28 18:30:00  25.566         27.304        25.476        short   \n", "55183 2025-07-28 18:45:00  25.586         27.114        25.417        short   \n", "55184 2025-07-28 19:00:00  25.816         27.023        25.417        short   \n", "55185 2025-07-28 19:15:00  25.713         26.794        25.417        short   \n", "\n", "      supertrend_state  \n", "55176            short  \n", "55177            short  \n", "55178            short  \n", "55179            short  \n", "55180            short  \n", "55181            short  \n", "55182            short  \n", "55183            short  \n", "55184            short  \n", "55185            short  \n", "\n", "Turtle State Distribution:\n", "turtle_state\n", "short      20401\n", "long       19978\n", "neutral    14807\n", "Name: count, dtype: int64\n", "\n", "Comparison with Supertrend:\n", "supertrend_state   long  short\n", "turtle_state                  \n", "long              16703   3275\n", "neutral            7306   7501\n", "short              2925  17476\n", "Kaydedildi: AVAX_15m_ATRW20_ATRm2_ATRst3.5_STOPm2_HLW20.xlsx\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20144\\846817941.py:405: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  profit_by_nratio = group.groupby(pd.cut(group['N_RATIO'], bins))['PROFIT'].sum()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["('AVAX', '15m', np.float64(3.5), np.int64(2), np.int64(2), np.int64(20)) için N_RATIO aralıklarına göre profit tablosu 'nratio_profit_AVAX_15m_3.5_2_2_20.xlsx' olarak kaydedildi.\n", "('AVAX', '15m', np.float64(3.5), np.int64(2), np.int64(2), np.int64(20)) i<PERSON>in EN İYİ TEK EŞİK: N_RATIO < 0.00730 ile toplam profit: 26236.08\n", "('AVAX', '15m', np.float64(3.5), np.int64(2), np.int64(2), np.int64(20)) için EN İYİ ARALIK: 0.00264 <= N_RATIO <= 0.00730 ile toplam profit: 26586.93\n", "Optimize sim<PERSON><PERSON>yon başlıyor: ('AVAX', '15m', np.float64(3.5), np.int64(2), np.int64(2), np.int64(20)) için best_lower=0.002636716651499787, best_upper=0.007296319924137676\n", "\n", "DataFrame Structure:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 55187 entries, 0 to 55186\n", "Data columns (total 33 columns):\n", " #   Column                     Non-Null Count  Dtype         \n", "---  ------                     --------------  -----         \n", " 0   Open Time                  55187 non-null  datetime64[ns]\n", " 1   Open                       55187 non-null  float64       \n", " 2   High                       55187 non-null  float64       \n", " 3   Low                        55187 non-null  float64       \n", " 4   Close                      55187 non-null  float64       \n", " 5   TR                         55187 non-null  float64       \n", " 6   ATR_EMA20                  55186 non-null  float64       \n", " 7   Low_20h                    55167 non-null  float64       \n", " 8   High_20h                   55167 non-null  float64       \n", " 9   Low_10h                    55177 non-null  float64       \n", " 10  High_10h                   55177 non-null  float64       \n", " 11  supertrend                 55187 non-null  float64       \n", " 12  supertrend_state           55187 non-null  object        \n", " 13  supertrend_upperband       55187 non-null  float64       \n", " 14  supertrend_lowerband       55187 non-null  float64       \n", " 15  donchian_high              55167 non-null  float64       \n", " 16  donchian_low               55167 non-null  float64       \n", " 17  turtle_state               55187 non-null  object        \n", " 18  State                      55187 non-null  object        \n", " 19  amount                     55187 non-null  float64       \n", " 20  entry_price                28012 non-null  float64       \n", " 21  pyramid                    55187 non-null  int64         \n", " 22  exit_reason                8645 non-null   object        \n", " 23  exit_price                 1300 non-null   float64       \n", " 24  commissions                55187 non-null  float64       \n", " 25  profit                     55187 non-null  float64       \n", " 26  entry_commissions          55187 non-null  float64       \n", " 27  exit_commissions           55187 non-null  float64       \n", " 28  profit_comission_included  55187 non-null  float64       \n", " 29  cumulative_profit          55187 non-null  float64       \n", " 30  max_possible_profit        55187 non-null  float64       \n", " 31  n_ratio                    1300 non-null   float64       \n", " 32  max_cum_profit_drawdown    55187 non-null  float64       \n", "dtypes: datetime64[ns](1), float64(27), int64(1), object(4)\n", "memory usage: 13.9+ MB\n", "None\n", "\n", "First 5 rows of DataFrame:\n", "            Open Time    Open    High     Low   Close     TR  ATR_EMA20  \\\n", "0 2023-12-31 23:00:00  38.254  38.596  38.154  38.442  0.442        NaN   \n", "1 2023-12-31 23:15:00  38.443  38.513  38.213  38.408  0.300   0.442000   \n", "2 2023-12-31 23:30:00  38.407  38.661  38.403  38.643  0.258   0.428476   \n", "3 2023-12-31 23:45:00  38.642  38.706  38.471  38.589  0.235   0.412240   \n", "4 2024-01-01 00:00:00  38.590  38.942  38.519  38.931  0.423   0.395360   \n", "\n", "   Low_20h  High_20h  Low_10h  ...  exit_price  commissions profit  \\\n", "0      NaN       NaN      NaN  ...         NaN          0.0    0.0   \n", "1      NaN       NaN      NaN  ...         NaN          0.0    0.0   \n", "2      NaN       NaN      NaN  ...         NaN          0.0    0.0   \n", "3      NaN       NaN      NaN  ...         NaN          0.0    0.0   \n", "4      NaN       NaN      NaN  ...         NaN          0.0    0.0   \n", "\n", "   entry_commissions  exit_commissions  profit_comission_included  \\\n", "0                0.0               0.0                        0.0   \n", "1                0.0               0.0                        0.0   \n", "2                0.0               0.0                        0.0   \n", "3                0.0               0.0                        0.0   \n", "4                0.0               0.0                        0.0   \n", "\n", "   cumulative_profit max_possible_profit n_ratio  max_cum_profit_drawdown  \n", "0                0.0                 0.0     NaN                      0.0  \n", "1                0.0                 0.0     NaN                      0.0  \n", "2                0.0                 0.0     NaN                      0.0  \n", "3                0.0                 0.0     NaN                      0.0  \n", "4                0.0                 0.0     NaN                      0.0  \n", "\n", "[5 rows x 33 columns]\n", "\n", "Turtle State Analysis:\n", "                Open Time   Close  donchian_high  donchian_low turtle_state  \\\n", "55177 2025-07-28 17:15:00  26.009         27.415        25.850        short   \n", "55178 2025-07-28 17:30:00  25.819         27.382        25.751        short   \n", "55179 2025-07-28 17:45:00  25.799         27.382        25.751        short   \n", "55180 2025-07-28 18:00:00  25.739         27.382        25.729        short   \n", "55181 2025-07-28 18:15:00  25.539         27.304        25.583        short   \n", "55182 2025-07-28 18:30:00  25.566         27.304        25.476        short   \n", "55183 2025-07-28 18:45:00  25.586         27.114        25.417        short   \n", "55184 2025-07-28 19:00:00  25.816         27.023        25.417        short   \n", "55185 2025-07-28 19:15:00  25.696         26.794        25.417        short   \n", "55186 2025-07-28 19:30:00  25.513         26.473        25.417        short   \n", "\n", "      supertrend_state  \n", "55177            short  \n", "55178            short  \n", "55179            short  \n", "55180            short  \n", "55181            short  \n", "55182            short  \n", "55183            short  \n", "55184            short  \n", "55185            short  \n", "55186            short  \n", "\n", "Turtle State Distribution:\n", "turtle_state\n", "short      20402\n", "long       19978\n", "neutral    14807\n", "Name: count, dtype: int64\n", "\n", "Comparison with Supertrend:\n", "supertrend_state   long  short\n", "turtle_state                  \n", "long              16703   3275\n", "neutral            7306   7501\n", "short              2925  17477\n", "Kaydedildi: AVAX_15m_ATRW20_ATRm2_ATRst3.5_STOPm2_HLW20.xlsx\n", "Optimize simülasyon sonucu kaydedildi: AVAX_15m_ATRW20_ATRm2_ATRst3.5_STOPm2_HLW20_OPTIMIZE.xlsx\n", "Mevcut sütunlar: ['Coin', 'Interval', 'ATR Multiplier', 'Supertrend ATR', 'Stop Multiplier', 'HighLow Window', 'Best N Ratio Lower', 'Best N Ratio Upper', 'Cumulative Trade Profit (comission included)', 'First Sim Cumulative Profit (comission included)', 'Total Commission', 'Monthly Avg Profit', 'Yearly Avg Profit', 'Max Drawdown Last', 'Max Drawdown Max', 'Max Consecutive Drawdown Zero False']\n", "<PERSON><PERSON><PERSON> ta<PERSON> 'tum_sonuclar_analiz_1.xlsx' dos<PERSON><PERSON>na kayd<PERSON>.\n"]}], "source": ["# data<PERSON>i kaydeden kod\n", "import traceback\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from binance.client import Client\n", "import time\n", "from datetime import datetime\n", "\n", "\n", "class BacktestMeta:\n", "    def __init__(self):\n", "        # Tek tip parametre ile birden fazla coin eklemek için:\n", "        default_params = {\n", "            'INTERVALS': ['15m'],\n", "            'ATR_WINDOWS': [20],\n", "            'ATR_MULTIPLIERS': [2],\n", "            'ATR_MULTIPLIERS_ST': [3.5],\n", "            'STOP_MULTIPLIERS': [2],\n", "            'N_RATIOS': [(1.00, 0.00)],\n", "            'HIGHLOW_WINDOWS': [20]\n", "        }\n", "        coin_list = ['AVAX']  # <PERSON><PERSON><PERSON> istediğin coinleri yazabilirsin\n", "\n", "        self.PARITE_PARAMETRELERI = {}\n", "        for coin in coin_list:\n", "            self.PARITE_PARAMETRELERI[coin] = default_params.copy()\n", "\n", "\n", "        # <PERSON><PERSON><PERSON> bazı coinler için özel parametre istiyo<PERSON>n, eski <PERSON> ekleyebilirsin:\n", "        # self.PARITE_PARAMETRELERI['TRX'] = {...}\n", "\n", "        self.COINS = list(self.PARITE_PARAMETRELERI.keys())\n", "        self.start_dt = datetime(2024, 1, 1, 0, 0, 0)\n", "        self.end_dt = datetime(2025, 12, 31, 23, 59, 59)\n", "        self.results = []\n", "        self.client = Client()\n", "        # Trading state variables\n", "        self.capital = None\n", "        self.balance = None\n", "        self.state = None\n", "        self.entry_price = None\n", "        self.entry_atr = None\n", "        self.pyramid = None\n", "        self.pyramid_entries = None\n", "        self.pyramid_amounts = None\n", "        self.max_price_during_trade = None\n", "        self.min_price_during_trade = None\n", "        self.max_pyramid = 4\n", "        self.commission_rate = 0.00045\n", "        self.cikis_sebebi = None\n", "        self.block_reason = None\n", "        # Tracking lists for simulation\n", "        self.states = []\n", "        self.capitals = []\n", "        self.balances = []\n", "        self.amounts = []\n", "        self.entry_prices = []\n", "        self.pyramids = []\n", "        self.exit_reasons = []\n", "        self.exit_prices = []\n", "        self.entry_block_reasons = []\n", "        self.profits = []\n", "        self.cumulative_profits = []\n", "        self.commissions = []\n", "        self.trade_profits = []\n", "        self.n_ratio_list = []\n", "        self.max_possible_profits = []\n", "        self.pyramid_entry_commissions = []  # Track entry commissions per pyramid entry\n", "        self.pyramid_exit_commissions = []  # Track exit commissions per pyramid exit\n", "        # Ensure state variables are always initialized to valid types\n", "        self.balance = 0.0\n", "        self.pyramid_entries = []\n", "        self.pyramid_amounts = []\n", "        self.first_entry_price = None  # EKLENDİ: <PERSON>lk piramit giriş fiyatı\n", "        self.entry_analysis = []  # Pozisyon analizlerini tutacak liste\n", "\n", "    def rma(self, series, window):\n", "        alpha = 1 / window\n", "        return series.ewm(alpha=alpha, adjust=False).mean()\n", "\n", "    def calc_atr_rma(self, df, window):\n", "        prev_close = df['Close'].shift(1)\n", "        tr1 = df['High'] - df['Low']\n", "        tr2 = (df['High'] - prev_close).abs()\n", "        tr3 = (df['Low'] - prev_close).abs()\n", "        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)\n", "        return self.rma(tr, window)\n", "\n", "    def calc_supertrend(self, df, atr_window, multiplier):\n", "        hl2 = (df['High'] + df['Low']) / 2\n", "        atr = self.calc_atr_rma(df, atr_window)\n", "        upperband = hl2 + multiplier * atr\n", "        lowerband = hl2 - multiplier * atr\n", "\n", "        final_upperband = upperband.copy()\n", "        final_lowerband = lowerband.copy()\n", "        supertrend = [np.nan] * len(df)\n", "        supertrend_state = ['neutral'] * len(df)\n", "\n", "        for i in range(len(df)):\n", "            if i == 0:\n", "                supertrend[i] = final_upperband.iloc[i]\n", "                if df['High'].iloc[i] < supertrend[i]:\n", "                    supertrend_state[i] = 'short'\n", "                elif df['Low'].iloc[i] > supertrend[i]:\n", "                    supertrend_state[i] = 'long'\n", "                else:\n", "                    supertrend_state[i] = 'short' if df['Close'].iloc[i] < supertrend[i] else 'long'\n", "                continue\n", "\n", "            if (upperband.iloc[i] < final_upperband.iloc[i - 1]) or (\n", "                    df['High'].iloc[i - 1] > final_upperband.iloc[i - 1]):\n", "                final_upperband.iloc[i] = upperband.iloc[i]\n", "            else:\n", "                final_upperband.iloc[i] = final_upperband.iloc[i - 1]\n", "\n", "            if (lowerband.iloc[i] > final_lowerband.iloc[i - 1]) or (\n", "                    df['Low'].iloc[i - 1] < final_lowerband.iloc[i - 1]):\n", "                final_lowerband.iloc[i] = lowerband.iloc[i]\n", "            else:\n", "                final_lowerband.iloc[i] = final_lowerband.iloc[i - 1]\n", "\n", "            prev_state = supertrend_state[i - 1]\n", "            if prev_state == 'long':\n", "                if df['Low'].iloc[i] < final_lowerband.iloc[i]:\n", "                    supertrend[i] = final_upperband.iloc[i]\n", "                    supertrend_state[i] = 'short'\n", "                else:\n", "                    supertrend[i] = final_lowerband.iloc[i]\n", "                    supertrend_state[i] = 'long'\n", "            elif prev_state == 'short':\n", "                if df['High'].iloc[i] > final_upperband.iloc[i]:\n", "                    supertrend[i] = final_lowerband.iloc[i]\n", "                    supertrend_state[i] = 'long'\n", "                else:\n", "                    supertrend[i] = final_upperband.iloc[i]\n", "                    supertrend_state[i] = 'short'\n", "            else:\n", "                supertrend[i] = final_upperband.iloc[i]\n", "                if df['High'].iloc[i] < supertrend[i]:\n", "                    supertrend_state[i] = 'short'\n", "                elif df['Low'].iloc[i] > supertrend[i]:\n", "                    supertrend_state[i] = 'long'\n", "                else:\n", "                    supertrend_state[i] = 'short' if df['Close'].iloc[i] < supertrend[i] else 'long'\n", "\n", "        df['supertrend'] = pd.Series(supertrend, index=df.index)\n", "        df['supertrend_state'] = pd.Series(supertrend_state, index=df.index)\n", "        df['supertrend_upperband'] = final_upperband\n", "        df['supertrend_lowerband'] = final_lowerband\n", "\n", "        return df\n", "\n", "    def calc_turtle_state(self, df, donchian_period=20):\n", "        df['donchian_high'] = df['High'].rolling(window=donchian_period).max().shift(1)\n", "        df['donchian_low'] = df['Low'].rolling(window=donchian_period).min().shift(1)\n", "        df['turtle_state'] = 'neutral'\n", "        for i in range(1, len(df)):\n", "            prev_state = df['turtle_state'].iloc[i - 1]\n", "            if prev_state == 'neutral':\n", "                if df['High'].iloc[i] > df['donchian_high'].iloc[i]:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'long'\n", "                elif df['Low'].iloc[i] < df['donchian_low'].iloc[i]:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'short'\n", "                else:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'neutral'\n", "            elif prev_state == 'long':\n", "                exit_period = donchian_period // 2\n", "                exit_low = df['Low'].iloc[max(0, i - exit_period):i].min()\n", "                full_switch_low = df['Low'].iloc[max(0, i - donchian_period):i].min()\n", "                if df['Low'].iloc[i] <= full_switch_low:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'short'\n", "                elif df['Low'].iloc[i] <= exit_low:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'neutral'\n", "                else:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'long'\n", "            elif prev_state == 'short':\n", "                exit_period = donchian_period // 2\n", "                exit_high = df['High'].iloc[max(0, i - exit_period):i].max()\n", "                full_switch_high = df['High'].iloc[max(0, i - donchian_period):i].max()\n", "                if df['High'].iloc[i] >= full_switch_high:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'long'\n", "                elif df['High'].iloc[i] >= exit_high:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'neutral'\n", "                else:\n", "                    df.loc[df.index[i], 'turtle_state'] = 'short'\n", "        return df\n", "\n", "    def check_indicator_switches(self, df, i):\n", "        turtle_switch = False\n", "        supertrend_switch = False\n", "        if i > 0:\n", "            if df['turtle_state'].iloc[i] != df['turtle_state'].iloc[i - 1]:\n", "                turtle_switch = True\n", "            if df['supertrend_state'].iloc[i] != df['supertrend_state'].iloc[i - 1]:\n", "                supertrend_switch = True\n", "        return turtle_switch, supertrend_switch\n", "\n", "    def fetch_klines(self, symbol, interval, start_time, end_time):\n", "        all_klines = []\n", "        limit = 1000\n", "        start_ts = int(start_time.timestamp() * 1000)\n", "        end_ts = int(end_time.timestamp() * 1000)\n", "        while start_ts < end_ts:\n", "            klines = self.client.futures_klines(\n", "                symbol=symbol,\n", "                interval=interval,\n", "                startTime=start_ts,\n", "                endTime=end_ts,\n", "                limit=limit\n", "            )\n", "            if not klines:\n", "                break\n", "            all_klines.extend(klines)\n", "            last_open_time = klines[-1][0]\n", "            start_ts = last_open_time + 1\n", "            time.sleep(0.2)\n", "        columns = [\n", "            'Open Time', 'Open', 'High', 'Low', 'Close', 'Volume',\n", "            'Close Time', 'Quote Asset Volume', 'Number of Trades',\n", "            'Taker Buy Base Asset Volume', 'Taker Buy Quote Asset Volume', 'Ignore'\n", "        ]\n", "        df = pd.DataFrame(all_klines, columns=columns)\n", "        df['Open Time'] = pd.to_datetime(df['Open Time'], unit='ms')\n", "        df['Close Time'] = pd.to_datetime(df['Close Time'], unit='ms')\n", "        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:\n", "            df[col] = df[col].astype(float)\n", "        return df\n", "\n", "    def _enter_position(self, position_type, entry_price, amount):\n", "        commission = amount * entry_price * self.commission_rate\n", "        total_cost = amount * entry_price + commission\n", "        if self.capital >= total_cost:\n", "            if position_type == 'long':\n", "                new_balance = amount\n", "                new_capital = self.capital - total_cost\n", "            else:\n", "                new_balance = -amount\n", "                new_capital = self.capital + amount * entry_price - commission\n", "            self.pyramid_entry_commissions = [commission]\n", "            return new_capital, new_balance\n", "        else:\n", "            return self.capital, 0\n", "\n", "    def _exit_position(self, position_type, exit_price):\n", "        bal = self.balance if self.balance is not None else 0.0\n", "        entries = self.pyramid_entries if self.pyramid_entries is not None else []\n", "        amounts = self.pyramid_amounts if self.pyramid_amounts is not None else []\n", "        entry_commissions = self.pyramid_entry_commissions if hasattr(self, 'pyramid_entry_commissions') else []\n", "        exit_commissions = []\n", "        per_entry_profits = []\n", "        self.exit_price = exit_price\n", "        if position_type == 'long':\n", "            new_capital = self.capital + bal * exit_price\n", "            for ep, am in zip(entries, amounts):\n", "                exit_comm = am * exit_price * self.commission_rate\n", "                per_entry_profits.append((exit_price - ep) * am)\n", "                exit_commissions.append(exit_comm)\n", "            realized_profit = sum(per_entry_profits)\n", "            max_profit = sum([(self.max_price_during_trade - ep) * am for ep, am in zip(entries, amounts)])\n", "        else:\n", "            new_capital = self.capital - abs(bal) * exit_price\n", "            for ep, am in zip(entries, amounts):\n", "                exit_comm = am * exit_price * self.commission_rate\n", "                per_entry_profits.append((ep - exit_price) * am)\n", "                exit_commissions.append(exit_comm)\n", "            realized_profit = sum(per_entry_profits)\n", "            max_profit = sum([(ep - self.min_price_during_trade) * am for ep, am in zip(entries, amounts)])\n", "        self.pyramid_exit_commissions = exit_commissions\n", "        total_entry_comm = sum(entry_commissions)\n", "        total_exit_comm = sum(exit_commissions)\n", "        new_capital = new_capital - total_entry_comm - total_exit_comm\n", "        return new_capital, realized_profit, max_profit\n", "\n", "    def _add_pyramid_position(self, position_type, pyramid_trigger, amount):\n", "        cap = self.capital if self.capital is not None else 0.0\n", "        entries = self.pyramid_entries if self.pyramid_entries is not None else []\n", "        amounts = self.pyramid_amounts if self.pyramid_amounts is not None else []\n", "        commission = amount * pyramid_trigger * self.commission_rate\n", "        total_cost = amount * pyramid_trigger + commission\n", "        if cap >= total_cost:\n", "            if position_type == 'long':\n", "                new_balance = amount\n", "                new_capital = cap - total_cost\n", "            else:\n", "                new_balance = -amount\n", "                new_capital = cap + amount * pyramid_trigger - commission\n", "            new_pyramid_entries = entries + [pyramid_trigger]\n", "            new_pyramid_amounts = amounts + [amount]\n", "            if hasattr(self, 'pyramid_entry_commissions') and self.pyramid_entry_commissions:\n", "                new_pyramid_entry_commissions = self.pyramid_entry_commissions + [commission]\n", "            else:\n", "                new_pyramid_entry_commissions = [commission]\n", "            self.pyramid_entry_commissions = new_pyramid_entry_commissions\n", "            return new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts\n", "        else:\n", "            return cap, 0, 0, entries, amounts\n", "\n", "    def _check_stop_loss(self, position_type, entry_price, entry_atr, stop_mult, current_price):\n", "        if position_type == 'long':\n", "            stop_price = entry_price - stop_mult * entry_atr\n", "            stop_triggered = current_price < stop_price\n", "        else:\n", "            stop_price = entry_price + stop_mult * entry_atr\n", "            stop_triggered = current_price > stop_price\n", "        return stop_triggered, stop_price\n", "\n", "    def _check_exit_conditions(self, position_type, turtle_price, supertrend_price):\n", "        if position_type == 'long':\n", "            return turtle_price if turtle_price > supertrend_price else supertrend_price\n", "        else:\n", "            return turtle_price if turtle_price < supertrend_price else supertrend_price\n", "\n", "    def _check_pyramid_conditions(self, position_type, current_price, prev_entry_price, entry_atr):\n", "        if position_type == 'long':\n", "            pyramid_trigger_price = prev_entry_price + 0.5 * entry_atr\n", "            pyramid_triggered = current_price > pyramid_trigger_price\n", "        else:\n", "            pyramid_trigger_price = prev_entry_price - 0.5 * entry_atr\n", "            pyramid_triggered = current_price < pyramid_trigger_price\n", "        return pyramid_triggered, pyramid_trigger_price\n", "\n", "    def _execute_pyramid_entry(self, position_type, current_price, prev_entry_price, entry_atr, amount):\n", "        pyr = self.pyramid if self.pyramid is not None else 0\n", "        max_pyr = self.max_pyramid if self.max_pyramid is not None else 0\n", "        cap = self.capital if self.capital is not None else 0.0\n", "        entries = self.pyramid_entries if self.pyramid_entries is not None else []\n", "        amounts = self.pyramid_amounts if self.pyramid_amounts is not None else []\n", "        if pyr >= max_pyr:\n", "            return cap, 0, 0, entries, amounts, pyr, prev_entry_price, False\n", "        pyramid_triggered, pyramid_trigger_price = self._check_pyramid_conditions(\n", "            position_type, current_price, prev_entry_price, entry_atr\n", "        )\n", "        if pyramid_triggered:\n", "            new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts = \\\n", "                self._add_pyramid_position(position_type, pyramid_trigger_price, amount)\n", "            if new_balance != 0:\n", "                new_pyramid = pyr + 1\n", "                new_entry_price = pyramid_trigger_price\n", "                return new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts, new_pyramid, new_entry_price, True\n", "            else:\n", "                return cap, 0, entries, amounts, pyr, prev_entry_price, False\n", "        else:\n", "            return cap, 0, entries, amounts, pyr, prev_entry_price, False\n", "\n", "    def run_backtest(self):\n", "        self.best_nratio_ranges = {}  # parametre tuple'ı -> (lower, upper)\n", "        for coin in self.COINS:\n", "            params = self.PARITE_PARAMETRELERI[coin]\n", "            for interval in params['INTERVALS']:\n", "                for atr_window in params['ATR_WINDOWS']:\n", "                    for atr_mult, stop_mult in zip(params['ATR_MULTIPLIERS'], params['STOP_MULTIPLIERS']):\n", "                        for highlow_window in params['HIGHLOW_WINDOWS']:\n", "                            for atr_mult_st in params['ATR_MULTIPLIERS_ST']:\n", "                                for n_ratio_upper, n_ratio_lower, in params['N_RATIOS']:\n", "                                    try:\n", "                                        symbol = f\"{coin}USDT\"\n", "                                        print(\n", "                                            f\"Başlıyor: {symbol} {interval} ATRwin:{atr_window} ATRmult:{atr_mult} STOPmult:{stop_mult} HLwin:{highlow_window}\")\n", "                                        df = self.fetch_klines(symbol, interval, self.start_dt, self.end_dt)\n", "                                        # ...devamı aynı...\n", "                                        df['Previous Close'] = df['Close'].shift(1)\n", "                                        df['TR'] = df[['High', 'Low', 'Previous Close']].apply(\n", "                                                lambda row: max(\n", "                                                row['High'] - row['Low'],\n", "                                                abs(row['High'] - row['Previous Close']),\n", "                                                abs(row['Low'] - row['Previous Close'])\n", "                                            ), axis=1\n", "                                        )\n", "                                        df[f'ATR_EMA{atr_window}'] = df['TR'].shift(1).ewm(span=atr_window,\n", "                                                                                        adjust=False).mean()\n", "                                        df[f'Low_{highlow_window}h'] = df['Low'].shift(1).rolling(\n", "                                            window=highlow_window).min()\n", "                                        df[f'High_{highlow_window}h'] = df['High'].shift(1).rolling(\n", "                                            window=highlow_window).max()\n", "                                        exit_window = int(highlow_window / 2)\n", "                                        df[f'Low_{exit_window}h'] = df['Low'].shift(1).rolling(window=exit_window).min()\n", "                                        df[f'High_{exit_window}h'] = df['High'].shift(1).rolling(window=exit_window).max()\n", "                                        df = self.calc_supertrend(df, atr_window, atr_mult_st)\n", "                                        df = self.calc_turtle_state(df, donchian_period=highlow_window)\n", "                                        self._run_trading_simulation(df, coin, interval, atr_window, atr_mult,\n", "                                                                 atr_mult_st, stop_mult, highlow_window, exit_window,\n", "                                                                 n_ratio_upper, n_ratio_lower)\n", "                                    except Exception as e:\n", "                                        traceback.print_exc()\n", "                                        print(\n", "                                            f\"HATA: {coin} {interval} {atr_window} {atr_mult} {atr_mult_st} {stop_mult} {highlow_window} -> {e}\")\n", "                                        continue\n", "        \n", "        # N Ratio <PERSON>!!!!\n", "        import pandas as pd\n", "        import numpy as np\n", "       \n", "        # ...run_backtest fonksiyonunun N_RATIO analiz bloğu...\n", "\n", "        entry_df = pd.DataFrame(self.entry_analysis)\n", "        entry_df_valid = entry_df[entry_df['N_RATIO'].notnull()]\n", "\n", "        param_cols = [\"COIN\", \"INTERVAL\", \"ATR_MULT_ST\", \"ATR_MULT\", \"STOP_MULT\", \"HIGHLOW_WINDOW\"]\n", "        grouped = entry_df_valid.groupby(param_cols)\n", "        bins = np.arange(0, 0.05, 0.001)\n", "\n", "        for params, group in grouped:\n", "            profit_by_nratio = group.groupby(pd.cut(group['N_RATIO'], bins))['PROFIT'].sum()\n", "            nratio_profit_df = profit_by_nratio.reset_index()\n", "            nratio_profit_df.columns = ['N_RATIO_BIN', 'TOTAL_PROFIT']\n", "            nratio_profit_df['N_RATIO_BIN'] = nratio_profit_df['N_RATIO_BIN'].astype(str)\n", "            out_file = f\"nratio_profit_{'_'.join([str(p) for p in params])}.xlsx\"\n", "\n", "            # --- EN İYİ EŞİK VE ARALIK HESAPLAMA ---\n", "            thresholds = np.unique(group['N_RATIO'].values)\n", "            best_profit = -np.inf\n", "            best_threshold = None\n", "            for t in thresholds:\n", "                profit = group[group['N_RATIO'] < t]['profit_comission_included'].sum()\n", "                if profit > best_profit:\n", "                    best_profit = profit\n", "                    best_threshold = t\n", "\n", "            best_range_profit = -np.inf\n", "            best_lower = None\n", "            best_upper = None\n", "            for i, lower in enumerate(thresholds):\n", "                for upper in thresholds[i+1:]:\n", "                    profit = group[(group['N_RATIO'] >= lower) & (group['N_RATIO'] <= upper)]['profit_comission_included'].sum()\n", "                    if profit > best_range_profit:\n", "                        best_range_profit = profit\n", "                        best_lower = lower\n", "                        best_upper = upper\n", "            self.best_nratio_ranges[params] = (best_lower, best_upper)\n", "\n", "            # --- EXCEL'E YAZMA ---\n", "            with pd.ExcelWriter(out_file, engine=\"xlsxwriter\") as writer:\n", "                nratio_profit_df.to_excel(writer, sheet_name=\"N_RATIO_PROFIT\", index=False)\n", "                best_df = pd.DataFrame([{\n", "                    \"BEST_THRESHOLD\": best_threshold,\n", "                    \"BEST_THRESHOLD_PROFIT_COMMISSION_INCLUDED\": best_profit,\n", "                    \"BEST_RANGE_LOWER\": best_lower,\n", "                    \"BEST_RANGE_UPPER\": best_upper,\n", "                    \"BEST_RANGE_PROFIT_COMMISSION_INCLUDED\": best_range_profit\n", "        }])\n", "                best_df.to_excel(writer, sheet_name=\"BEST_N_RATIO\", index=False)\n", "\n", "            print(f\"{params} için N_RATIO aralıklarına göre profit tablosu '{out_file}' olarak kaydedildi.\")\n", "            print(f\"{params} için EN İYİ TEK EŞİK: N_RATIO < {best_threshold:.5f} ile toplam profit: {best_profit:.2f}\")\n", "            print(f\"{params} için EN İYİ ARALIK: {best_lower:.5f} <= N_RATIO <= {best_upper:.5f} ile toplam profit: {best_range_profit:.2f}\")\n", "            \n", "            # Şimdi en iyi aralıklarla tekrar simülasyon başlatıyoruz\n", "\n", "        for params, (best_lower, best_upper) in self.best_nratio_ranges.items():\n", "            # Optimize simülasyon başlamadan önce analiz listelerini temizle\n", "            self.entry_analysis = []\n", "            self.states = []\n", "            self.capitals = []\n", "            self.balances = []\n", "            self.amounts = []\n", "            self.entry_prices = []\n", "            self.pyramids = []\n", "            self.exit_reasons = []\n", "            self.exit_prices = []\n", "            self.entry_block_reasons = []\n", "            self.profits = []\n", "            self.cumulative_profits = []\n", "            self.commissions = []\n", "            self.trade_profits = []\n", "            self.n_ratio_list = []\n", "            self.max_possible_profits = []\n", "            self.pyramid_entry_commissions = []\n", "            self.pyramid_exit_commissions = []\n", "            self.entry_commissions = []\n", "            self.exit_commissions = []\n", "            self.how_many_hours = []\n", "            self.first_entry_price = None\n", "            coin, interval, atr_mult_st, atr_mult, stop_mult, highlow_window = params\n", "            n_ratio_lower, n_ratio_upper = best_lower, best_upper  \n", "            atr_window = 20  # veya parametrelerden çekebilirsin\n", "\n", "            symbol = f\"{coin}USDT\"\n", "            print(f\"Optimize simülasyon başlıyor: {params} için best_lower={best_lower}, best_upper={best_upper}\")\n", "\n", "            df = self.fetch_klines(symbol, interval, self.start_dt, self.end_dt)\n", "            df['Previous Close'] = df['Close'].shift(1)\n", "            df['TR'] = df[['High', 'Low', 'Previous Close']].apply(\n", "                lambda row: max(\n", "                    row['High'] - row['Low'],\n", "                    abs(row['High'] - row['Previous Close']),\n", "                    abs(row['Low'] - row['Previous Close'])\n", "                ), axis=1\n", "            )\n", "            df[f'ATR_EMA{atr_window}'] = df['TR'].shift(1).ewm(span=atr_window, adjust=False).mean()\n", "            df[f'Low_{highlow_window}h'] = df['Low'].shift(1).rolling(window=highlow_window).min()\n", "            df[f'High_{highlow_window}h'] = df['High'].shift(1).rolling(window=highlow_window).max()\n", "            exit_window = int(highlow_window / 2)\n", "            df[f'Low_{exit_window}h'] = df['Low'].shift(1).rolling(window=exit_window).min()\n", "            df[f'High_{exit_window}h'] = df['High'].shift(1).rolling(window=exit_window).max()\n", "            df = self.calc_supertrend(df, atr_window, atr_mult_st)\n", "            df = self.calc_turtle_state(df, donchian_period=highlow_window)\n", "\n", "            # --- EN ÖNEMLİ KISIM ---\n", "            self._run_trading_simulation(\n", "                df, coin, interval, atr_window, atr_mult, atr_mult_st, stop_mult,\n", "                highlow_window, exit_window, n_ratio_upper, n_ratio_lower, best_lower, best_upper\n", "            )\n", "            \n", "            # --- OPTİMİZE SONUÇLARI EXCEL'E YAZ ---\n", "            excel_file_opt = f\"{coin}_{interval}_ATRW{atr_window}_ATRm{atr_mult}_ATRst{atr_mult_st}_STOPm{stop_mult}_HLW{highlow_window}_OPTIMIZE.xlsx\"\n", "            with pd.ExcelWriter(excel_file_opt, engine=\"xlsxwriter\") as writer:\n", "                df.to_excel(writer, index=False)\n", "                worksheet = writer.sheets[\"Sheet1\"]\n", "                worksheet.freeze_panes(1, 1)\n", "            print(f\"Optimize simülasyon sonucu kaydedildi: {excel_file_opt}\")\n", "        self._process_final_results()\n", "    def add_max_column(self, df):\n", "        # Yeni kolon: max_cum_profit_drawdown\n", "        max_cum = None\n", "        freeze = False\n", "        max_cum_list = []\n", "        for val in df['cumulative_profit']:\n", "            if max_cum is None or val > max_cum:\n", "                max_cum = val\n", "                freeze = False\n", "                max_cum_list.append(0)\n", "            elif val < max_cum:\n", "                freeze = True\n", "                max_cum_list.append(max_cum - val)\n", "            else:\n", "                max_cum_list.append(0)\n", "        return max_cum_list\n", "\n", "    def reset(self, new_capital):\n", "        self.entry_price = None\n", "        self.entry_atr = None\n", "        self.pyramid = 0\n", "        self.pyramid_entries = []\n", "        self.pyramid_amounts = []\n", "        self.pyramid_entry_commissions = []\n", "        self.pyramid_exit_commissions = []\n", "        self.max_price_during_trade = None\n", "        self.min_price_during_trade = None\n", "        self.capital = new_capital\n", "        self.balance = 0\n", "        self.state = 'neutral'\n", "        self.first_entry_price = None  # Pozisyon kapandı, sı<PERSON><PERSON><PERSON>a\n", "\n", "    def update_commissions_profit(self, i, realized_profit, max_profit):\n", "        entry_index = i - self.how_many_hours[i]\n", "        self.entry_commissions[entry_index] = sum(self.pyramid_entry_commissions)\n", "        self.exit_commissions[entry_index] = sum(self.pyramid_exit_commissions)\n", "        self.cumulative_profits[entry_index] = realized_profit - sum(\n", "            self.pyramid_entry_commissions) - sum(\n", "            self.exit_commissions)\n", "        self.trade_profits[entry_index] = realized_profit\n", "        self.max_possible_profits[entry_index] = max_profit\n", "\n", "    def _run_trading_simulation(self, df, coin, interval, atr_window, atr_mult, atr_mult_st, stop_mult, highlow_window, exit_window, n_ratio_upper, n_ratio_lower, best_lower=None, best_upper=None):\n", "        initial_capital = 100000000\n", "        self.capital = initial_capital\n", "        self.balance = 0.0\n", "        self.state = 'neutral'\n", "        self.entry_price = None\n", "        self.entry_atr = None\n", "        self.pyramid = 0\n", "        self.n_ratio_list = []\n", "        self.max_price_during_trade = None\n", "        self.min_price_during_trade = None\n", "        self.cikis_sebebi = None\n", "        self.block_reason = None\n", "        self.states = []\n", "        self.capitals = []\n", "        self.balances = []\n", "        self.amounts = []\n", "        self.entry_prices = []\n", "        self.pyramids = []\n", "        self.exit_reasons = []\n", "        self.exit_prices = []\n", "        self.entry_block_reasons = []\n", "        self.profits = []\n", "        self.cumulative_profits = []\n", "        self.commissions = []\n", "        self.trade_profits = []\n", "        self.max_possible_profits = []\n", "        self.pyramid_entry_commissions = []\n", "        self.pyramid_exit_commissions = []\n", "        self.entry_commissions = []\n", "        self.exit_commissions = []\n", "        self.balance = 0.0\n", "        self.pyramid_entries = []\n", "        self.pyramid_amounts = []\n", "        self.how_many_hours = []\n", "        self.first_entry_price = None  # EKLENDİ: <PERSON>lk piramit giriş fiyatı\n", "\n", "        for i, row in df.iterrows():\n", "            high = row['High']\n", "            low = row['Low']\n", "            atr = row[f'ATR_EMA{atr_window}']\n", "            high_exit = row.get(f'High_{exit_window}h', np.nan)\n", "            low_exit = row.get(f'Low_{exit_window}h', np.nan)\n", "            st_up = row.get('supertrend_upperband', np.nan)\n", "            st_low = row.get('supertrend_lowerband', np.nan)\n", "\n", "            self.cikis_sebebi = None\n", "            self.exit_price = None\n", "            self.block_reason = None\n", "            realized_profit = 0\n", "            self.trade_profits.append(0)\n", "            self.cumulative_profits.append(0)\n", "            self.entry_commissions.append(0)\n", "            self.exit_commissions.append(0)\n", "            self.max_possible_profits.append(0)\n", "            self.n_ratio_list.append(None)\n", "\n", "            if self.state == 'neutral':\n", "                current_atr = atr\n", "            else:\n", "                current_atr = self.entry_atr\n", "\n", "            amount = 0 if (current_atr is None or np.isnan(\n", "                current_atr) or current_atr == 0) else 20 / (atr_mult * current_atr)\n", "\n", "            prev_state = self.state\n", "            prev_entry_price = self.entry_price\n", "\n", "            current_turtle_state = df['turtle_state'].iloc[i]\n", "            current_supertrend_state = df['supertrend_state'].iloc[i]\n", "\n", "            # n_ratio hesaplama ve sabitleme\n", "            if prev_state == 'neutral':\n", "                self.try_enter_position(row, atr_mult, atr_window, highlow_window, df, i, n_ratio_upper, n_ratio_lower, coin, interval, atr_mult_st, stop_mult, highlow_window, best_lower, best_upper)\n", "                self.how_many_hours.append(0)\n", "\n", "            if prev_state == 'long':\n", "                self.how_many_hours.append(self.how_many_hours[-1] + 1)\n", "                if self.max_price_during_trade is not None:\n", "                    self.max_price_during_trade = max(self.max_price_during_trade, high)\n", "                if self.min_price_during_trade is not None:\n", "                    self.min_price_during_trade = min(self.min_price_during_trade, low)\n", "                stop_triggered, stop_price = self._check_stop_loss('long', self.entry_price, self.entry_atr, stop_mult,\n", "                                                                   low)\n", "                if stop_triggered:\n", "                    new_capital, realized_profit, max_profit = self._exit_position(\n", "                        'long', stop_price\n", "                    )\n", "                    self.update_commissions_profit(i, realized_profit, max_profit)\n", "                    self.max_possible_profits[-1] = max_profit  # Sadece <PERSON> gü<PERSON>lle\n", "                    self.cikis_sebebi = 'Long Stop-Loss'\n", "                    if self.entry_analysis:\n", "                        self.entry_analysis[-1][\"PROFIT\"] = realized_profit\n", "                        self.entry_analysis[-1][\"profit_comission_included\"] = realized_profit - sum(self.pyramid_entry_commissions) - sum(self.pyramid_exit_commissions)\n", "                    self.reset(new_capital)\n", "                    self.try_enter_position(\n", "                        row, atr_mult, atr_window, highlow_window, df, i, n_ratio_upper, n_ratio_lower,  coin, interval, atr_mult_st, stop_mult, highlow_window\n", "                    )\n", "                    current_n_ratio = None\n", "                elif current_turtle_state in ['short', 'neutral'] and current_supertrend_state == 'short':\n", "                    final_exit_price = self._check_exit_conditions('long', low_exit, st_low)\n", "                    if final_exit_price:\n", "                        new_capital, realized_profit, max_profit = self._exit_position(\n", "                            'long', final_exit_price\n", "                        )\n", "                        self.update_commissions_profit(i, realized_profit, max_profit)\n", "                        self.max_possible_profits[-1] = max_profit  # Sadece <PERSON> gü<PERSON>lle          \n", "                        self.reset(new_capital)\n", "                        self.cikis_sebebi = f'Long Low{exit_window} Çıkışı'\n", "                        if self.entry_analysis:\n", "                            self.entry_analysis[-1][\"PROFIT\"] = realized_profit\n", "                            self.entry_analysis[-1][\"profit_comission_included\"] = realized_profit - sum(self.pyramid_entry_commissions) - sum(self.pyramid_exit_commissions)\n", "                        self.try_enter_position(\n", "                            row, atr_mult, atr_window, highlow_window, df, i, n_ratio_upper, n_ratio_lower, coin, interval, atr_mult_st, stop_mult, highlow_window\n", "                        )\n", "                        current_n_ratio = None\n", "                    else:\n", "                        self.cikis_sebebi = 'Pozisyon Açık'\n", "                elif self.pyramid < self.max_pyramid:\n", "                    piramit_yapildi = False\n", "                    while self.pyramid < self.max_pyramid:\n", "                        new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts, new_pyramid, new_entry_price, pyramid_executed = \\\n", "                            self._execute_pyramid_entry('long', high, self.entry_price, self.entry_atr, amount)\n", "                        if pyramid_executed:\n", "                            self.capital = new_capital\n", "                            self.balance += new_balance\n", "                            self.pyramid_entries = new_pyramid_entries\n", "                            self.pyramid_amounts = new_pyramid_amounts\n", "                            self.pyramid = new_pyramid\n", "                            self.entry_price = new_entry_price\n", "                            piramit_yapildi = True\n", "                        else:\n", "                            break\n", "                    self.cikis_sebebi = 'Long Pyramid' if pira<PERSON>_yapildi else 'Pozisyon Açık'\n", "\n", "            elif prev_state == 'short':\n", "                self.how_many_hours.append(self.how_many_hours[-1] + 1)\n", "                if self.max_price_during_trade is not None:\n", "                    self.max_price_during_trade = max(self.max_price_during_trade, high)\n", "                if self.min_price_during_trade is not None:\n", "                    self.min_price_during_trade = min(self.min_price_during_trade, low)\n", "                stop_triggered, stop_price = self._check_stop_loss('short', self.entry_price, self.entry_atr, stop_mult,\n", "                                                                   high)\n", "                if stop_triggered:\n", "                    new_capital, realized_profit, max_profit = self._exit_position(\n", "                        'short', stop_price\n", "                    )\n", "                    self.update_commissions_profit(i, realized_profit, max_profit)\n", "                    self.max_possible_profits[-1] = max_profit  # Sadece <PERSON> gü<PERSON>lle\n", "                    self.reset(new_capital)\n", "                    self.cikis_sebebi = 'Short Stop-Loss'\n", "                    if self.entry_analysis:\n", "                        self.entry_analysis[-1][\"PROFIT\"] = realized_profit\n", "                        self.entry_analysis[-1][\"profit_comission_included\"] = realized_profit - sum(self.pyramid_entry_commissions) - sum(self.pyramid_exit_commissions)\n", "                    self.try_enter_position(\n", "                        row, atr_mult, atr_window, highlow_window, df, i, n_ratio_upper, n_ratio_lower, coin, interval, atr_mult_st, stop_mult, highlow_window\n", "                    )\n", "                    current_n_ratio = None\n", "                elif (not np.isnan(high_exit) and high > high_exit) and current_supertrend_state == 'long':\n", "                    final_exit_price = self._check_exit_conditions('short', high_exit, st_up)\n", "                    if final_exit_price:\n", "                        new_capital, realized_profit, max_profit = self._exit_position(\n", "                            'short', final_exit_price\n", "                        )\n", "                        self.update_commissions_profit(i, realized_profit, max_profit)\n", "                        self.max_possible_profits[-1] = max_profit  # Sadece ka<PERSON> g<PERSON>l\n", "                        self.reset(new_capital)\n", "                        self.cikis_sebebi = f'Short High{exit_window} Çıkışı'\n", "                        if self.entry_analysis:\n", "                            self.entry_analysis[-1][\"PROFIT\"] = realized_profit\n", "                            self.entry_analysis[-1][\"profit_comission_included\"] = realized_profit - sum(self.pyramid_entry_commissions) - sum(self.pyramid_exit_commissions)\n", "                        self.try_enter_position(\n", "                            row, atr_mult, atr_window, highlow_window, df, i, n_ratio_upper, n_ratio_lower, coin, interval, atr_mult_st, stop_mult, highlow_window\n", "                        )\n", "                        current_n_ratio = None\n", "                    else:\n", "                        self.cikis_sebebi = 'Pozisyon Açık'\n", "                elif self.pyramid < self.max_pyramid:\n", "                    piramit_yapildi = False\n", "                    while self.pyramid < self.max_pyramid:\n", "                        new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts, new_pyramid, new_entry_price, pyramid_executed = \\\n", "                            self._execute_pyramid_entry('short', low, self.entry_price, self.entry_atr, amount)\n", "                        if pyramid_executed:\n", "                            self.capital = new_capital\n", "                            self.balance += new_balance\n", "                            self.pyramid_entries = new_pyramid_entries\n", "                            self.pyramid_amounts = new_pyramid_amounts\n", "                            self.pyramid = new_pyramid\n", "                            self.entry_price = new_entry_price\n", "                            piramit_yapildi = True\n", "                        else:\n", "                            break\n", "                    self.cikis_sebebi = 'Short Pyramid' if piramit_yapildi else 'Pozisyon Açık'\n", "\n", "            self.profits.append(realized_profit)\n", "            self.exit_prices.append(self.exit_price)\n", "            self.entry_block_reasons.append(self.block_reason)\n", "            self.states.append(self.state)\n", "            self.capitals.append(self.capital)\n", "            self.balances.append(self.balance)\n", "            self.amounts.append(amount)\n", "            self.entry_prices.append(self.entry_price)\n", "            self.pyramids.append(self.pyramid)\n", "            self.exit_reasons.append(self.cikis_sebebi)\n", "\n", "        df['State'] = self.states\n", "        df['capital'] = self.capitals\n", "        df['balance'] = self.balances\n", "        df['amount'] = self.amounts\n", "        df['entry_price'] = self.entry_prices\n", "        df['pyramid'] = self.pyramids\n", "        df['exit_reason'] = self.exit_reasons\n", "        df['exit_price'] = self.exit_prices\n", "        df['commissions'] = [a + b for a, b in zip(self.entry_commissions, self.exit_commissions)]\n", "        df['entry_block_reason'] = self.entry_block_reasons\n", "        df['profit'] = self.trade_profits\n", "        df['entry_commissions'] = self.entry_commissions\n", "        df['exit_commissions'] = self.exit_commissions\n", "        df['profit_comission_included'] = df['profit'] - df['entry_commissions'] - df['exit_commissions']\n", "        df['cumulative_profit'] = df['profit_comission_included'].cumsum()\n", "        df['trade_profit'] = self.trade_profits\n", "        df['max_possible_profit'] = self.max_possible_profits\n", "        df['n_ratio'] = self.n_ratio_list\n", "        df['max_cum_profit_drawdown'] = self.add_max_column(df)\n", "\n", "        drop_cols = ['Volume', 'Close Time', 'Quote Asset Volume', 'Number of Trades', 'Taker Buy Base Asset Volume',\n", "                     'Taker Buy Quote Asset Volume', 'Ignore', 'Previous Close', 'capital', 'balance',\n", "                     'entry_block_reason', 'trade_profit']\n", "        for col in drop_cols:\n", "            if col in df.columns:\n", "                df.drop(col, axis=1, inplace=True)\n", "\n", "        print(\"\\nDataFrame Structure:\")\n", "        print(df.info())\n", "        print(\"\\nFirst 5 rows of DataFrame:\")\n", "        print(df.head())\n", "        print(\"\\nTurtle State Analysis:\")\n", "        print(df[['Open Time', 'Close', 'donchian_high', 'donchian_low', 'turtle_state', 'supertrend_state']].tail(10))\n", "        print(\"\\nTurtle State Distribution:\")\n", "        print(df['turtle_state'].value_counts())\n", "        print(\"\\nComparison with Supertrend:\")\n", "        state_comparison = pd.crosstab(df['turtle_state'], df['supertrend_state'])\n", "        print(state_comparison)\n", "\n", "        excel_file = f\"{coin}_{interval}_ATRW{atr_window}_ATRm{atr_mult}_ATRst{atr_mult_st}_STOPm{stop_mult}_HLW{highlow_window}.xlsx\"\n", "        with pd.ExcelWriter(excel_file, engine=\"xlsxwriter\") as writer:\n", "            df.to_excel(writer, index=False)\n", "            worksheet = writer.sheets[\"Sheet1\"]\n", "            worksheet.freeze_panes(1, 1)\n", "        print(f\"Kay<PERSON><PERSON><PERSON>: {excel_file}\")\n", "\n", "        df['year'] = df['Open Time'].dt.year\n", "        yearly_trade_profits = df.groupby('year')['cumulative_profit'].sum().to_dict()\n", "        profit_2022 = yearly_trade_profits.get(2022, 0)\n", "        profit_2023 = yearly_trade_profits.get(2023, 0)\n", "        profit_2024 = yearly_trade_profits.get(2024, 0)\n", "        profit_2025 = yearly_trade_profits.get(2025, 0)\n", "\n", "        last_cum_profit = df['cumulative_profit'].iloc[-1] if 'cumulative_profit' in df.columns else 0\n", "        trade_count = sum([1 for k in self.commissions if k > 0])\n", "        total_commission = sum(self.commissions)\n", "        trade_volume = total_commission / self.commission_rate if self.commission_rate > 0 else 0\n", "        self.results.append({\n", "            \"COIN\": coin,\n", "            \"INTERVAL\": interval,\n", "            \"ATR_WINDOW\": atr_window,\n", "            \"ATR_MULT\": atr_mult,\n", "            \"ATR_MULT_ST\": atr_mult_st,\n", "            \"STOP_MULT\": stop_mult,\n", "            \"HIGHLOW_WINDOW\": highlow_window,\n", "            \"PROFIT_2022\": profit_2022,\n", "            \"PROFIT_2023\": profit_2023,\n", "            \"PROFIT_2024\": profit_2024,\n", "            \"PROFIT_2025\": profit_2025,\n", "            \"CUMULATIVE_PROFIT\": last_cum_profit,\n", "            \"TRADE_COUNT\": trade_count,\n", "            \"TOTAL_COMMISSION\": total_commission,\n", "            \"TRADE_VOLUME\": trade_volume\n", "        })\n", "\n", "    # ...class BacktestMeta: içinde...\n", "\n", "\n", "    # ...existing code...\n", "\n", "    # ...existing code...\n", "\n", "    def _process_final_results(self):\n", "        import pandas as pd\n", "\n", "        summary_rows = []\n", "        monthly_rows = []\n", "        drawdown_zero_rows = []\n", "        consecutive_false_summary = {}\n", "\n", "        # İlk simülasyon sonuçlarını sakla\n", "        first_sim_cumprofits = {}\n", "        for result in self.results:\n", "            key = (\n", "                result['COIN'], result['INTERVAL'], result['ATR_MULT_ST'],\n", "                result['ATR_MULT'], result['STOP_MULT'], result['HIGHLOW_WINDOW']\n", "        )\n", "        first_sim_cumprofits[key] = result['CUMULATIVE_PROFIT']\n", "\n", "        # Optimize simülasyon sonuçlarını oku\n", "        for params, (best_lower, best_upper) in self.best_nratio_ranges.items():\n", "            coin, interval, atr_mult_st, atr_mult, stop_mult, highlow_window = params\n", "            atr_window = 20  # veya parametrelerden çekebilirsin\n", "            excel_file_opt = f\"{coin}_{interval}_ATRW{atr_window}_ATRm{atr_mult}_ATRst{atr_mult_st}_STOPm{stop_mult}_HLW{highlow_window}_OPTIMIZE.xlsx\"\n", "            try:\n", "                df = pd.read_excel(excel_file_opt)\n", "                df['year_month'] = df['Open Time'].dt.to_period('M')\n", "                monthly_profit = df.groupby('year_month')['profit_comission_included'].sum()\n", "                yearly_profit = df.groupby(df['Open Time'].dt.year)['profit_comission_included'].sum()\n", "                max_drawdown_last = df['max_cum_profit_drawdown'].iloc[-1]\n", "                max_drawdown_max = df['max_cum_profit_drawdown'].max()\n", "                drawdown_months = df.groupby('year_month')['max_cum_profit_drawdown'].apply(lambda x: (x == 0).any())\n", "                false_streak = 0\n", "                max_false_streak = 0\n", "                for is_zero in drawdown_months.values:\n", "                    if not is_zero:\n", "                        false_streak += 1\n", "                        if false_streak > max_false_streak:\n", "                            max_false_streak = false_streak\n", "                    else:\n", "                        false_streak = 0\n", "                consecutive_false_summary[coin] = max_false_streak\n", "\n", "                for ym, is_zero in drawdown_months.items():\n", "                    drawdown_zero_rows.append({\n", "                        \"Coin\": coin,\n", "                        \"Year-Month\": str(ym),\n", "                        \"Drawdown Zero\": is_zero\n", "                    })\n", "\n", "                for ym, val in monthly_profit.items():\n", "                    monthly_rows.append({\n", "                        \"Coin\": coin,\n", "                        \"Year-Month\": str(ym),\n", "                        \"Monthly Profit\": val\n", "                    })\n", "                monthly_rows.append({\n", "                    \"Coin\": coin,\n", "                    \"Year-Month\": \"Ortalama\",\n", "                    \"Monthly Profit\": monthly_profit.mean()\n", "                })\n", "\n", "                # İlk simülasyon cumulative profit (comission included)\n", "                first_key = (coin, interval, atr_mult_st, atr_mult, stop_mult, highlow_window)\n", "                first_cumprofit = first_sim_cumprofits.get(first_key, None)\n", "\n", "                # Özet tabloya optimize parametreleri ve en iyi n_ratio aralığını ekle\n", "                summary_rows.append({\n", "                    \"Coin\": coin,\n", "                    \"Interval\": interval,\n", "                    \"ATR Multiplier\": atr_mult,\n", "                    \"Supertrend ATR\": atr_mult_st,\n", "                    \"Stop Multiplier\": stop_mult,\n", "                    \"HighLow Window\": highlow_window,\n", "                    \"Best N Ratio Lower\": best_lower,\n", "                    \"Best N Ratio Upper\": best_upper,\n", "                    \"Cumulative Trade Profit (comission included)\": df['profit_comission_included'].sum(),\n", "                    \"First Sim Cumulative Profit (comission included)\": first_cumprofit,\n", "                    \"Total Commission\": df['commissions'].sum(),\n", "                    \"Monthly Avg Profit\": monthly_profit.mean(),\n", "                    \"Yearly Avg Profit\": yearly_profit.mean(),\n", "                    \"Max Drawdown Last\": max_drawdown_last,\n", "                    \"Max Drawdown Max\": max_drawdown_max,\n", "                    \"Max Consecutive Drawdown Zero False\": max_false_streak,\n", "                })\n", "\n", "            except Exception as e:\n", "                print(f\"<PERSON><PERSON><PERSON> oluşturulamadı: {excel_file_opt} - {e}\")\n", "                continue\n", "\n", "        summary_df = pd.DataFrame(summary_rows)\n", "        print(\"Mevcut sütunlar:\", summary_df.columns.tolist())\n", "\n", "        desired_order = [\n", "            \"Coin\", \"Interval\", \"ATR Multiplier\", \"Supertrend ATR\", \"Stop Multiplier\", \"HighLow Window\",\n", "            \"Best N Ratio Lower\", \"Best N Ratio Upper\",\n", "            \"Cumulative Trade Profit (comission included)\",\n", "            \"First Sim Cumulative Profit (comission included)\",\n", "            \"Total Commission\", \"Monthly Avg Profit\", \"Yearly Avg Profit\",\n", "            \"Max Drawdown Last\", \"Max Drawdown Max\", \"Max Consecutive Drawdown Zero False\"\n", "        ]\n", "\n", "        missing = [col for col in desired_order if col not in summary_df.columns]\n", "        extra = [col for col in summary_df.columns if col not in desired_order]\n", "\n", "        if missing:\n", "            print(\"Eksik veya yanlış isim<PERSON> s<PERSON>:\", missing)\n", "        if extra:\n", "            print(\"Beklenmeyen/ekstra sütunlar:\", extra)\n", "        if missing:\n", "            raise Exception(\"Sütun isimleri uyuşmuyor. Yukarıdaki eksik/fazla sütunları düzeltin.\")\n", "\n", "        summary_df = summary_df[desired_order]\n", "        monthly_df = pd.DataFrame(monthly_rows)\n", "        drawdown_zero_df = pd.DataFrame(drawdown_zero_rows)\n", "\n", "        # <PERSON><PERSON><PERSON> sırasını ayarla: <PERSON>val ikinci sırada olacak\n", "        desired_order = [\"Coin\", \"Interval\", \"ATR Multiplier\", \"Supertrend ATR\", \"Stop Multiplier\", \"HighLow Window\",\n", "                        \"Best N Ratio Lower\", \"Best N Ratio Upper\",\n", "                        \"Cumulative Trade Profit (comission included)\",\n", "                        \"First Sim Cumulative Profit (comission included)\",\n", "                        \"Total Commission\", \"Monthly Avg Profit\", \"Yearly Avg Profit\",\n", "                        \"Max Drawdown Last\", \"Max Drawdown Max\", \"Max Consecutive Drawdown Zero False\"]\n", "        summary_df = summary_df[desired_order]\n", "\n", "        # Sıralama: Monthly Avg Profit'e göre büyükten küçüğe\n", "        summary_df = summary_df.sort_values(by=\"Monthly Avg Profit\", ascending=False)\n", "\n", "        # Excel'e yaz ve biçimlendir\n", "        with pd.ExcelWriter(\"tum_sonuclar_analiz_1.xlsx\", engine=\"xlsxwriter\") as writer:\n", "            summary_df.to_excel(writer, sheet_name=\"OzetTablo\", index=False)\n", "            monthly_df.to_excel(writer, sheet_name=\"AylikKarZarar\", index=False)\n", "            drawdown_zero_df.to_excel(writer, sheet_name=\"DrawdownSifirAylar\", index=False)\n", "\n", "            workbook = writer.book\n", "            worksheet = writer.sheets[\"OzetTablo\"]\n", "\n", "            # Filtreyi aç ve ilk satırı freeze et\n", "            worksheet.autofilter(0, 0, summary_df.shape[0], summary_df.shape[1] - 1)\n", "            worksheet.freeze_panes(1, 0)\n", "\n", "            # Tüm tabloya border ekle\n", "            border_format = workbook.add_format({'border': 1})\n", "\n", "            # <PERSON><PERSON><PERSON><PERSON>ı<PERSON>i\n", "            color1 = workbook.add_format({'bg_color': '#F2F2F2', 'border': 1})\n", "            color2 = workbook.add_format({'bg_color': '#DDEBF7', 'border': 1})\n", "\n", "            # Başlıkları bold yap\n", "            header_format = workbook.add_format({'bold': True, 'border': 1, 'bg_color': '#B7DEE8'})\n", "            for col_num, value in enumerate(summary_df.columns.values):\n", "                worksheet.write(0, col_num, value, header_format)\n", "\n", "            # Coin adlarını bold yap\n", "            bold_format = workbook.add_format({'bold': True, 'border': 1})\n", "\n", "            # Satırları yaz ve biçimlendir\n", "            for row_num, row_data in enumerate(summary_df.values, start=1):\n", "                fmt = color1 if row_num % 2 == 0 else color2\n", "                for col_num, cell_data in enumerate(row_data):\n", "                    if col_num == 0:  # Coin adı sütunu\n", "                        worksheet.write(row_num, col_num, cell_data, bold_format)\n", "                    else:\n", "                        worksheet.write(row_num, col_num, cell_data, fmt)\n", "\n", "            # DrawdownSifirAylar tablosunun en altına summary ekle\n", "            worksheet_dd = writer.sheets[\"DrawdownSifirAylar\"]\n", "            last_row = len(drawdown_zero_df) + 2\n", "            worksheet_dd.write(last_row, 0, \"Max Consecutive Drawdown Zero False Summary\")\n", "            for idx, (coin, streak) in enumerate(consecutive_false_summary.items()):\n", "                worksheet_dd.write(last_row + idx + 1, 0, coin, bold_format)\n", "                worksheet_dd.write(last_row + idx + 1, 1, streak, border_format)\n", "\n", "        print(\"<PERSON><PERSON><PERSON> tablo<PERSON> 'tum_sonuclar_analiz_1.xlsx' dosyasına kaydedildi.\")\n", "\n", "        if not summary_rows:\n", "            print(\"<PERSON>ç sonuç bulunamadı.\")\n", "\n", "    def try_enter_position(self, row, atr_mult, atr_window, highlow_window, df, i, n_ratio_upper, n_ratio_lower, coin, interval, atr_mult_st, stop_mult, highlow_window_param, best_lower=None, best_upper=None):\n", "        high = row['High']\n", "        low = row['Low']\n", "        atr = row[f'ATR_EMA{atr_window}']\n", "        high_hl = row[f'High_{highlow_window}h']\n", "        low_hl = row[f'Low_{highlow_window}h']\n", "        st_up = row.get('supertrend_upperband', None)\n", "        st_low = row.get('supertrend_lowerband', None)\n", "        turtle_switch, supertrend_switch = self.check_indicator_switches(df, i)\n", "        current_turtle_state = df['turtle_state'].iloc[i]\n", "        current_supertrend_state = df['supertrend_state'].iloc[i]\n", "        self.block_reason = None\n", "        current_atr = atr\n", "        amount = 0 if (current_atr is None or np.isnan(current_atr) or current_atr == 0) else 20 / (\n", "                    atr_mult * current_atr)\n", "\n", "        position_type = None\n", "        entry_price = 1\n", "\n", "\n", "        if turtle_switch and supertrend_switch:\n", "            if current_turtle_state == 'long' and current_supertrend_state == 'long':\n", "                position_type = 'long'\n", "                entry_price = high_hl if high_hl > st_up else st_up\n", "            elif current_turtle_state == 'short' and current_supertrend_state == 'short':\n", "                position_type = 'short'\n", "                entry_price = low_hl if low_hl < st_low else st_low\n", "        elif turtle_switch or supertrend_switch:\n", "            if current_turtle_state == 'long' and current_supertrend_state == 'long':\n", "                position_type = 'long'\n", "                entry_price = high_hl if turtle_switch and not supertrend_switch else st_up\n", "            elif current_turtle_state == 'short' and current_supertrend_state == 'short':\n", "                position_type = 'short'\n", "                entry_price = low_hl if turtle_switch and not supertrend_switch else st_low\n", "        elif (current_turtle_state == 'long' and current_supertrend_state == 'long') or \\\n", "                (current_turtle_state == 'short' and current_supertrend_state == 'short'):\n", "            if current_turtle_state == 'long' and high > high_hl:\n", "                position_type = 'long'\n", "                entry_price = high_hl\n", "            elif current_turtle_state == 'short' and low < low_hl:\n", "                position_type = 'short'\n", "                entry_price = low_hl\n", "            else:\n", "                self.block_reason = 'Fiyat koşulları sağlanmadı' if position_type is None else '<PERSON>yat yeterli degil'\n", "\n", "        calc_n_ratio = atr / entry_price\n", "\n", "        # --- EN İYİ ARALIK FİLTRESİ ---\n", "        if best_lower is not None and best_upper is not None:\n", "            if not (best_lower <= calc_n_ratio <= best_upper):\n", "                self.block_reason = 'N Ratio Best Range Filter'\n", "                return\n", "\n", "        if calc_n_ratio > n_ratio_upper or calc_n_ratio < n_ratio_lower:\n", "            self.block_reason = 'N Ratio'\n", "            return\n", "\n", "        elif position_type and entry_price:\n", "            new_capital, new_balance = self._enter_position(\n", "                position_type, entry_price, amount\n", "            )\n", "            if new_balance != 0:\n", "                # N_RATIO hesaplaması!!!!!!!\n", "                if self.first_entry_price is None:\n", "                    self.first_entry_price = entry_price\n", "                current_n_ratio = atr / self.first_entry_price if self.first_entry_price else None\n", "                \n", "                # ...try_enter_position fonksiyonu içinde...\n", "                self.entry_analysis.append({\n", "                    \"COIN\": coin,\n", "                    \"INTERVAL\": interval,\n", "                    \"N_RATIO\": current_n_ratio,\n", "                    \"ENTRY_PRICE\": entry_price,\n", "                    \"PROFIT\": 0,  # <PERSON><PERSON><PERSON><PERSON> kapanı<PERSON> güncellenebilir\n", "                    \"profit_comission_included\": 0,  # <PERSON><PERSON><PERSON>on kapanınca güncellenebilir\n", "                    \"OPEN_TIME\": row['Open Time'],\n", "                    \"ATR_MULT_ST\": atr_mult_st,\n", "                    \"ATR_MULT\": atr_mult,\n", "                    \"STOP_MULT\": stop_mult,\n", "                    \"HIGHLOW_WINDOW\": highlow_window\n", "                })\n", "                self.state = position_type\n", "                self.entry_price = entry_price\n", "                if self.first_entry_price is None:\n", "                    self.first_entry_price = entry_price\n", "\n", "                if self.first_entry_price is None:\n", "                    self.first_entry_price = entry_price\n", "\n", "                \n", "                self.n_ratio_list[i] = current_n_ratio\n", "                self.how_many_hours[-1] = 0  # reset counter for instant switch position within the same hour\n", "\n", "                self.entry_atr = atr\n", "                self.pyramid = 1\n", "                self.capital = new_capital\n", "                self.balance = new_balance\n", "                self.pyramid_entries = [entry_price]\n", "                self.pyramid_amounts = [amount]\n", "                self.max_price_during_trade = row['High']\n", "                self.min_price_during_trade = row['Low']\n", "                self.cikis_sebebi = 'Açık Pozisyon'\n", "\n", "                # PROFIT VE MAX_POSSIBLE_PROFIT İŞLEM AÇILIŞINDA YAZILIYOR\n", "                self.trade_profits[i] = 0  # Açılışta profit 0\n", "                self.max_possible_profits[i] = 0  # Açılışta max_possible_profit 0\n", "\n", "                while self.pyramid < self.max_pyramid:\n", "                    new_capital, new_balance, new_pyramid_entries, new_pyramid_amounts, new_pyramid, new_entry_price, pyramid_executed = \\\n", "                        self._execute_pyramid_entry(\n", "                            position_type, high if position_type == 'long' else low, self.entry_price, self.entry_atr,\n", "                            amount\n", "                        )\n", "                    if pyramid_executed:\n", "                        self.capital = new_capital\n", "                        self.balance += new_balance\n", "                        self.pyramid_entries = new_pyramid_entries\n", "                        self.pyramid_amounts = new_pyramid_amounts\n", "                        self.pyramid = new_pyramid\n", "                        self.entry_price = new_entry_price\n", "                        self.cikis_sebebi = f'{position_type} Pyramid'\n", "                    else:\n", "                        break\n", "            else:\n", "                self.block_reason = '<PERSON><PERSON><PERSON>'\n", "\n", "\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    backtest = BacktestMeta()\n", "    backtest.run_backtest()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.4"}}, "nbformat": 4, "nbformat_minor": 5}
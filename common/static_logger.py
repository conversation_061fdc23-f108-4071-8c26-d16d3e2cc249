import logging
import os
from datetime import datetime
import pytz


class LimitedLinesFileHandler(logging.FileHandler):
    MAX_LINES = 1000000
    CHECK_INTERVAL = 20000

    def __init__(self, filename, mode='a', encoding=None, delay=False):
        self.max_lines = self.__class__.MAX_LINES
        self.check_interval = self.__class__.CHECK_INTERVAL
        self.line_count = 0
        # If the file exists, determine how many lines it already has.
        if os.path.exists(filename):
            with open(filename, 'r', encoding=encoding) as f:
                self.line_count = sum(1 for _ in f)
        super().__init__(filename, mode, encoding, delay)

    def emit(self, record):
        try:
            super().emit(record)
        except Exception:
            self.handleError(record)
        self.line_count += 1

        if self.line_count % self.check_interval == 0:
            self._truncate_if_necessary()

    def _truncate_if_necessary(self):
        if self.line_count > self.max_lines:
            with open(self.baseFilename, 'r', encoding=self.encoding) as f:
                lines = f.readlines()
            half = len(lines) // 2
            new_lines = lines[half:]
            with open(self.baseFilename, 'w', encoding=self.encoding) as f:
                f.writelines(new_lines)
            self.line_count = len(new_lines)


class ContextFilter(logging.Filter):
    """Filter to add custom fields to log records"""
    def filter(self, record):
        if not hasattr(record, 'custom_time'):
            utc_now = datetime.now(pytz.UTC)
            record.custom_time = utc_now.strftime('%Y-%m-%d %H:%M:%S UTC')
        return True

def get_logger_(coin):
    logger = logging.getLogger(f'{coin}_logger')
    
    # Check if logger already has our handler
    for handler in logger.handlers:
        if getattr(handler, 'name', None) == f'{coin}_logger':
            return logger

    logger.setLevel(logging.INFO)
    logger.addFilter(ContextFilter())
    
    # Create logs directory if it doesn't exist
    os.makedirs('./log_files', exist_ok=True)
    
    file_handler = LimitedLinesFileHandler(f'./log_files/{coin}_bot.log')
    file_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S %Z')
    formatter.converter = lambda *args: datetime.now(pytz.UTC).timetuple()
    file_handler.setFormatter(formatter)
    file_handler.name = f'{coin}_logger'
    logger.addHandler(file_handler)
    return logger


class BacktestLogAdapter(logging.LoggerAdapter):
    """Adapter to add timestamp to backtest logs"""
    def process(self, msg, kwargs):
        timestamp = self.extra.get('timestamp')
        # Always ensure the timestamp includes 'UTC' to make it clear
        if timestamp and 'UTC' not in timestamp:
            timestamp = f"{timestamp} UTC"
        elif not timestamp:
            utc_now = datetime.now(pytz.UTC)
            timestamp = utc_now.strftime('%Y-%m-%d %H:%M:%S UTC')
        return f"{timestamp} - {msg}", kwargs

def get_logger_backtest(coin):
    # Create logger if it doesn't exist
    logger = logging.getLogger(f'{coin}_backtest')
    
    # Remove any existing handlers to ensure we start fresh
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)
    
    # Set up logger
    logger.setLevel(logging.DEBUG)
    
    # Create logs directory if it doesn't exist
    os.makedirs('./log_files', exist_ok=True)
    
    # Create file handler with mode='w' to overwrite the file
    file_handler = LimitedLinesFileHandler(f'./log_files/{coin}_backtest.log', mode='w')
    file_handler.setLevel(logging.DEBUG)
    file_handler.name = f'{coin}_backtest'
    
    # Create formatter with UTC time
    class UTCFormatter(logging.Formatter):
        def formatTime(self, record, datefmt=None):
            dt = datetime.fromtimestamp(record.created, pytz.UTC)
            if datefmt:
                return dt.strftime(datefmt)
            else:
                return dt.strftime('%Y-%m-%d %H:%M:%S UTC')
    
    formatter = UTCFormatter('%(message)s')
    file_handler.setFormatter(formatter)
    
    # Add handler to logger
    logger.addHandler(file_handler)
    
    return BacktestLogAdapter(logger, {'timestamp': None})

# Example usage:
# if __name__ == '__main__':
#     coin_logger = get_logger_('bitcoin', max_lines=1000, check_interval=100)
#     for i in range(1100):
#         coin_logger.info(f"Log message {i}")

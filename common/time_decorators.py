import functools
import time


def make_time_loop_decorator(interval, mod_func=None, fallback_index=1, use_fallback=True, default_return=None):
    """
    Factory function to create a time loop decorator with tracking of last execution.

    The decorated function should expect:
      - The first positional argument to be the current time (in seconds).
      - The second positional argument to be the fallback value if the time condition is not met.

    Args:
        interval (int): The time interval in seconds to check.
        mod_func (callable, optional): A function to transform the first argument (e.g., lambda t: t % 60).
        fallback_index (int): The index of the argument to return as a fallback when the condition isn't met.
        use_fallback (bool): If True, return args[fallback_index] when the condition isn't met.
                             If False, return the provided default_return value.
        default_return: The value to return when use_fallback is False and the condition isn't met.
    """

    def decorator(func):
        # Store last execution time for this specific function
        last_execution = {"timestamp": 0}

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get the current time from the first positional argument
            current_time = args[0]
            if mod_func is not None:
                time_val = mod_func(current_time)
            else:
                time_val = current_time

            # Calculate time since last execution
            time_since_last = current_time - last_execution["timestamp"]

            # Check if we need to execute (either by exact timing or because we missed a cycle)
            should_execute = (time_val % interval == 0) or (time_since_last >= interval)

            if should_execute:
                last_execution["timestamp"] = current_time
                return func(*args, **kwargs)
            return args[fallback_index] if use_fallback else default_return

        return wrapper

    return decorator


# Create decorators using the factory
two_seconds_loop = make_time_loop_decorator(2)
ten_seconds_loop = make_time_loop_decorator(10, mod_func=lambda t: t % 60)
five_mins_loop = make_time_loop_decorator(300)
fifteen_mins_loop = make_time_loop_decorator(900)
one_hour_loop = make_time_loop_decorator(3600)
six_hours_loop = make_time_loop_decorator(21600, use_fallback=False, default_return=None)

# Example usage of the time loop decorators
#
# In this example, the function 'process_data' will only execute its logic if the condition is met.
# Otherwise, it will return the fallback value provided as the second argument.
#
# @ten_seconds_loop
# def process_data(timestamp, fallback_value):
#     """Processes data only when (timestamp % 60) is divisible by 10."""
#     return f"Processed at {timestamp}"
#
# # The function will only process data if (timestamp % 60) is divisible by 10.
# # For example:
# print(process_data(30, "Fallback"))  # Expected output: "Processed at 30" 
#                                       # (30 % 60 = 30, and 30 % 10 == 0)
# print(process_data(31, "Fallback"))  # Expected output: "Fallback" 
#                                       # (31 % 60 = 31, and 31 % 10 != 0)

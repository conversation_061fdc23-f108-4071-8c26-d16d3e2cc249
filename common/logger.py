import logging
from functools import wraps
from typing import Type, TypeVar, Callable


class LineLimitHandler(logging.FileHandler):
    def __init__(self, filename: str, mode: str = 'a', encoding: str = None, delay: bool = False,
                 max_lines: int = 10000):
        super().__init__(filename, mode, encoding, delay)
        self.max_lines = max_lines

    def emit(self, record: logging.LogRecord) -> None:
        super().emit(record)
        self.flush()
        self.truncate_if_needed()

    def truncate_if_needed(self):
        try:
            with open(self.baseFilename, 'r+', encoding=self.encoding) as f:
                lines = f.readlines()
                total_lines = len(lines)
                if total_lines > self.max_lines:
                    keep_lines = total_lines // 2
                    remaining_lines = lines[keep_lines:]
                    f.seek(0)
                    f.truncate()
                    f.writelines(remaining_lines)
        except Exception as e:
            print(f"Failed to truncate log file {self.baseFilename}: {e}")


class FFLogger(logging.Logger):
    EXTRA_VERBOSE_LEVEL_NUM = 5
    VERBOSE_LEVEL_NUM = 15
    PRODUCTION_LEVEL_NUM = 25

    def __init__(self, name: str, level: int = logging.NOTSET):
        super().__init__(name, level)

    def extra_verbose(self, msg: str, *args, **kwargs) -> None:
        if self.isEnabledFor(self.EXTRA_VERBOSE_LEVEL_NUM):
            self._log(self.EXTRA_VERBOSE_LEVEL_NUM, msg, args, **kwargs)

    def verbose(self, msg: str, *args, **kwargs) -> None:
        if self.isEnabledFor(self.VERBOSE_LEVEL_NUM):
            self._log(self.VERBOSE_LEVEL_NUM, msg, args, **kwargs)

    def production(self, msg: str, *args, **kwargs) -> None:
        if self.isEnabledFor(self.PRODUCTION_LEVEL_NUM):
            self._log(self.PRODUCTION_LEVEL_NUM, msg, args, **kwargs)


logging.setLoggerClass(FFLogger)

logging.addLevelName(FFLogger.EXTRA_VERBOSE_LEVEL_NUM, "EXTRA_VERBOSE")
logging.addLevelName(FFLogger.VERBOSE_LEVEL_NUM, "VERBOSE")
logging.addLevelName(FFLogger.PRODUCTION_LEVEL_NUM, "PRODUCTION")

T = TypeVar('T')


class Logger:
    _loggers = {}

    def __init__(self, coin: str, max_lines: int = 1000):
        self.coin = coin
        self.logger = self._get_logger(coin, max_lines)

    @classmethod
    def _get_logger(cls, coin: str, max_lines: int) -> FFLogger:
        if coin in cls._loggers:
            return cls._loggers[coin]

        logger = logging.getLogger(f'{coin}_logger')
        logger.setLevel(FFLogger.EXTRA_VERBOSE_LEVEL_NUM)

        if not logger.handlers:
            file_handler = LineLimitHandler(f'{coin}_bot.log', max_lines=max_lines)
            file_handler.setLevel(FFLogger.EXTRA_VERBOSE_LEVEL_NUM)
            formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        cls._loggers[coin] = logger
        return logger

    def get_logger(self) -> FFLogger:
        return self.logger


def inject_logger(coin: str, level: str = 'INFO', max_lines: int = 1000) -> Callable[[Type[T]], Type[T]]:
    level = level.upper()

    def decorator(cls: Type[T]) -> Type[T]:
        logger_instance = Logger(coin, max_lines=max_lines)
        logger = logger_instance.get_logger()

        if level == 'EXTRA_VERBOSE':
            logger.setLevel(FFLogger.EXTRA_VERBOSE_LEVEL_NUM)
        elif level == 'VERBOSE':
            logger.setLevel(FFLogger.VERBOSE_LEVEL_NUM)
        elif level == 'PRODUCTION':
            logger.setLevel(FFLogger.PRODUCTION_LEVEL_NUM)
        elif level == 'DEBUG':
            logger.setLevel(logging.DEBUG)
        elif level == 'INFO':
            logger.setLevel(logging.INFO)
        elif level == 'WARNING':
            logger.setLevel(logging.WARNING)
        elif level == 'ERROR':
            logger.setLevel(logging.ERROR)
        elif level == 'CRITICAL':
            logger.setLevel(logging.CRITICAL)
        else:
            logger.setLevel(logging.INFO)

        setattr(cls, 'logger', logger)
        return cls

    return decorator


def log_method(func: Callable) -> Callable:
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        self.logger.info(f"Entering {func.__name__} with args={args}, kwargs={kwargs}")
        self.logger.verbose(f"Detailed args: args={args}, kwargs={kwargs}")
        result = func(self, *args, **kwargs)
        self.logger.info(f"Exiting {func.__name__} with result={result}")
        self.logger.verbose(f"Detailed result: result={result}")
        return result

    return wrapper




# example usage of the logger
#
# @inject_logger(coin='BTC', level='DEBUG')
# class Example:
#     @log_method
#     def sample_method(self, value):
#         return value * 2
#
#
# example = Example()
# example.sample_method(5)
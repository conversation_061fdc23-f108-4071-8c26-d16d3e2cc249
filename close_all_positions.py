#!/usr/bin/env python3
"""
Close All Futures Positions Script

This script closes all open futures positions on Binance.
Use with caution - this will close ALL positions immediately at market price.

Usage:
    python close_all_positions.py [--dry-run] [--symbol SYMBOL]

Options:
    --dry-run    Show what would be closed without actually executing
    --symbol     Close only specific symbol (e.g., BTCUSDT)
"""

import argparse
import json
import logging
import sys
from typing import List, Dict, Optional

from binance import Client
from binance.exceptions import BinanceAPIException, BinanceRequestException

try:
    from common.logger import setup_logger
except ImportError:
    # Fallback for standalone usage
    import logging

    def setup_logger(name, log_file):
        """Fallback logger setup"""
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # File handler
        try:
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        except:
            pass  # Ignore file logging errors

        return logger


def load_config():
    """Load configuration from config.py or environment variables"""
    try:
        from config import API_KEY, API_SECRET
        return {
            'binance': {
                'api_key': API_KEY,
                'api_secret': API_SECRET
            }
        }
    except ImportError:
        # Fallback to environment variables
        import os
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')

        if not api_key or not api_secret:
            raise ValueError("API credentials not found in config.py or environment variables")

        return {
            'binance': {
                'api_key': api_key,
                'api_secret': api_secret
            }
        }


class PositionCloser:
    def __init__(self, api_key: str, api_secret: str, logger: logging.Logger):
        """
        Initialize the position closer

        Args:
            api_key: Binance API key
            api_secret: Binance API secret
            logger: Logger instance
        """
        self.client = Client(api_key, api_secret)
        self.logger = logger

    def get_all_positions(self) -> List[Dict]:
        """
        Get all futures positions with non-zero amounts

        Returns:
            List of position dictionaries
        """
        try:
            positions = self.client.futures_position_information()
            # Filter only positions with non-zero amounts
            open_positions = [
                pos for pos in positions
                if float(pos['positionAmt']) != 0
            ]
            return open_positions
        except (BinanceAPIException, BinanceRequestException) as e:
            self.logger.error(f"Error fetching positions: {e}")
            raise

    def get_position_by_symbol(self, symbol: str) -> Optional[Dict]:
        """
        Get position for a specific symbol

        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)

        Returns:
            Position dictionary or None if no position
        """
        try:
            positions = self.client.futures_position_information(symbol=symbol)
            if positions and float(positions[0]['positionAmt']) != 0:
                return positions[0]
            return None
        except (BinanceAPIException, BinanceRequestException) as e:
            self.logger.error(f"Error fetching position for {symbol}: {e}")
            raise

    def close_position(self, position: Dict, dry_run: bool = False) -> bool:
        """
        Close a single position

        Args:
            position: Position dictionary from Binance API
            dry_run: If True, only log what would be done

        Returns:
            True if successful, False otherwise
        """
        symbol = position['symbol']
        position_amt = float(position['positionAmt'])
        entry_price = float(position['entryPrice'])
        unrealized_pnl = float(position['unRealizedProfit'])
        mark_price = float(position['markPrice'])

        # Determine side for closing order
        if position_amt > 0:
            side = 'SELL'
            position_type = 'LONG'
        else:
            side = 'BUY'
            position_type = 'SHORT'

        close_amount = abs(position_amt)

        # Calculate position value
        position_value = close_amount * mark_price

        self.logger.info(f"{'[DRY RUN] ' if dry_run else ''}Closing {position_type} position:")
        self.logger.info(f"  Symbol: {symbol}")
        self.logger.info(f"  Amount: {close_amount}")
        self.logger.info(f"  Entry Price: {entry_price}")
        self.logger.info(f"  Mark Price: {mark_price}")
        self.logger.info(f"  Position Value: {position_value:.2f} USDT")
        self.logger.info(f"  Unrealized PnL: {unrealized_pnl:.4f} USDT")
        self.logger.info(f"  Close Side: {side}")

        if dry_run:
            return True

        try:
            # Get current symbol info for quantity precision
            exchange_info = self.client.futures_exchange_info()
            symbol_info = next((s for s in exchange_info['symbols'] if s['symbol'] == symbol), None)

            if symbol_info:
                # Adjust quantity to proper precision
                quantity_precision = symbol_info['quantityPrecision']
                close_amount = round(close_amount, quantity_precision)

            # Execute market order to close position
            order = self.client.futures_create_order(
                symbol=symbol,
                side=side,
                type='MARKET',
                quantity=close_amount,
                reduceOnly=True
            )

            self.logger.info(f"✅ Position closed successfully:")
            self.logger.info(f"  Order ID: {order['orderId']}")
            self.logger.info(f"  Status: {order['status']}")

            return True

        except (BinanceAPIException, BinanceRequestException) as e:
            self.logger.error(f"❌ Error closing position for {symbol}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error closing position for {symbol}: {e}")
            return False

    def close_all_positions(self, target_symbol: Optional[str] = None, dry_run: bool = False) -> Dict[str, int]:
        """
        Close all open positions or a specific symbol

        Args:
            target_symbol: If provided, close only this symbol
            dry_run: If True, only show what would be closed

        Returns:
            Dictionary with success/failure counts
        """
        results = {'success': 0, 'failed': 0, 'total': 0}

        try:
            if target_symbol:
                # Close specific symbol
                position = self.get_position_by_symbol(target_symbol)
                if position:
                    results['total'] = 1
                    if self.close_position(position, dry_run):
                        results['success'] = 1
                    else:
                        results['failed'] = 1
                else:
                    self.logger.info(f"No open position found for {target_symbol}")
            else:
                # Close all positions
                positions = self.get_all_positions()
                results['total'] = len(positions)

                if not positions:
                    self.logger.info("No open positions found")
                    return results

                self.logger.info(f"Found {len(positions)} open position(s)")

                for position in positions:
                    if self.close_position(position, dry_run):
                        results['success'] += 1
                    else:
                        results['failed'] += 1

        except Exception as e:
            self.logger.error(f"Error in close_all_positions: {e}")
            results['failed'] = results['total']

        return results


def test_connection(closer: PositionCloser) -> bool:
    """Test API connection and permissions"""
    try:
        # Test basic connection
        account_info = closer.client.futures_account()
        closer.logger.info("✅ API connection successful")

        # Test permissions by getting positions (read-only)
        positions = closer.get_all_positions()
        closer.logger.info(f"✅ Permissions verified - found {len(positions)} open positions")

        return True
    except Exception as e:
        closer.logger.error(f"❌ Connection/permission test failed: {e}")
        return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Close all futures positions')
    parser.add_argument('--dry-run', action='store_true',
                        help='Show what would be closed without executing')
    parser.add_argument('--symbol', type=str,
                        help='Close only specific symbol (e.g., BTCUSDT)')

    parser.add_argument('--test', action='store_true',
                        help='Test API connection and permissions only')

    args = parser.parse_args()

    # Setup logger
    logger = setup_logger('position_closer', 'close_positions.log')

    try:
        # Load configuration
        config = load_config()

        if 'binance' not in config or 'api_key' not in config['binance']:
            logger.error("Binance API credentials not found in config")
            sys.exit(1)

        api_key = config['binance']['api_key']
        api_secret = config['binance']['api_secret']

        # Validate API credentials
        if not api_key or not api_secret:
            logger.error("API key or secret is empty")
            sys.exit(1)

        # Create position closer
        closer = PositionCloser(api_key, api_secret, logger)

        # Test connection if requested
        if args.test:
            logger.info("Testing API connection and permissions...")
            if test_connection(closer):
                logger.info("✅ All tests passed!")
            else:
                logger.error("❌ Tests failed!")
                sys.exit(1)
            return

        # Confirmation prompt (skip for dry run)
        if not args.dry_run:
            if args.symbol:
                confirm_msg = f"Are you sure you want to close position for {args.symbol}? (yes/no): "
            else:
                confirm_msg = "⚠️  WARNING: This will close ALL open futures positions! Are you sure? (yes/no): "

            confirmation = input(confirm_msg).lower().strip()
            if confirmation not in ['yes', 'y']:
                logger.info("Operation cancelled by user")
                sys.exit(0)

        # Execute closing
        logger.info("=" * 60)
        if args.dry_run:
            logger.info("DRY RUN MODE - No actual orders will be placed")
        logger.info("Starting position closing process...")
        logger.info("=" * 60)

        results = closer.close_all_positions(args.symbol, args.dry_run)

        # Summary
        logger.info("=" * 60)
        logger.info("SUMMARY:")
        logger.info(f"Total positions: {results['total']}")
        logger.info(f"Successfully closed: {results['success']}")
        logger.info(f"Failed to close: {results['failed']}")

        if results['failed'] > 0:
            logger.warning("Some positions failed to close. Check the logs above.")
            sys.exit(1)
        elif results['total'] > 0:
            logger.info("✅ All positions closed successfully!")
        else:
            logger.info("ℹ️  No positions to close")

    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
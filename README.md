# super-archelon


super archelon gonna fill these later 

# How to run a bot

- create a machine we use ubuntu t2.micro or t3.micro 8gb default values
- get the connection string
- go to the folder that the pem file exists and connect to the machine
- after that run these commands:
```commandline
sudo apt update
sudo apt install docker.io
```
- create a token in github for cloning repo
- after that run these commands:
```commandline
git config --global credential.helper store
git clone https://github.com/FFAutomaton/super-archelon.git
```
- enter github user name and token you generated
- enter the repo dir
- create a file called config `sudo nano config.py`
```commandline
API_KEY="xxxx"
API_SECRET="xxx"
```
- save the file ctrl+x, enter, enter
- update the run.sh
```commandline
sudo nano bash_scripts/run.sh
```
- make sure the json file exist with the true configurations under `config/ENAUSDT`
```commandline
{
    "interval": "15m",
    "amount_from_config": 15,
    "leverage": 20,
    "atr_multiplier": 1.5,
    "atr_stop_multiplier": 1.5,
    "supertrend_atr": 4,
    "atr_filter_upper": 0.0098,
    "atr_filter_lower": 0.0,
    "position_preference": "both"
}
```
- run this command to run the bot in the repo main directory
```commandline
sudo bash bash_scripts/run.sh
```
- if you want to reset the bot, stop docker, delete the files updated,
.pkl state file and log files.
```commandline
sudo docker stop $(sudo docker ps -q)
git reset --hard
sudo rm config/traders/*.pkl
sudo rm log_files/*.log
```
#!/bin/bash

# Array of tokens
TOKENS=("AVAXUSDT")
BOT_ACCOUNT=""

# Stop and remove any existing containers
for token in "${TOKENS[@]}"; do
    DOCKER_ID=$(docker ps -aqf "name=super_archelon_$token")
    docker stop $DOCKER_ID
done
echo "stopped containers"

sudo docker system prune -f
echo "pruned unused Docker data"

docker build --rm -t super_archelon ./

echo "built image"
# Loop through the tokens and create containers for each one
for token in "${TOKENS[@]}"; do
    # Define the container name based on the token
    CONTAINER_NAME="super_archelon_$token"

    # Run the Docker container with the adjusted container name and TOKEN environment variable
    docker run -t -i -d \
        --name $CONTAINER_NAME \
        -v /home/<USER>/super-archelon:/app \
        -e TOKEN=$token \
        -e BOT_ACCOUNT=$BOT_ACCOUNT \
        --restart=always \
        super_archelon
    echo "started container for $token"
done
